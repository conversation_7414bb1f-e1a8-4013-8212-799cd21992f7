// src/world_info_optimizer/ui/modals.ts

import { ModalOptions, EntryEditorOptions, RegexEditorOptions, LorebookEntry, TavernRegex } from '../types';
import { escapeHtml } from './helpers';

export const showModal = (options: ModalOptions): Promise<string | boolean> => {
    const Swal = window.parent.Swal;
    if (!Swal) {
        return new Promise((resolve, reject) => {
            const result = window.parent.prompt(options.text, typeof options.value === 'string' ? options.value : '');
            if (result !== null) resolve(result);
            else reject(new Error('Prompt cancelled'));
        });
    }

    const type = options.type || 'alert';

    switch (type) {
        case 'confirm':
            return Swal.fire({
                title: options.title || '确认',
                text: options.text,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '确认',
                cancelButtonText: '取消',
            }).then((result: any) => {
                if (result.isConfirmed) {
                    return true;
                }
                throw new Error('Confirmation cancelled');
            });
        
        case 'prompt':
            return Swal.fire({
                title: options.title,
                text: options.text,
                input: 'text',
                inputValue: options.value || '',
                showCancelButton: true,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then((result: any) => {
                if (result.isConfirmed && typeof result.value === 'string') {
                    return result.value;
                }
                throw new Error('Prompt cancelled');
            });

        case 'alert':
        default:
            return Swal.fire({
                title: options.title || '提示',
                text: options.text,
                icon: 'info',
            }).then(() => true);
    }
};

export const showSuccessTick = (message: string = '操作成功', duration: number = 1500) => {
    const Swal = window.parent.Swal;
    if (!Swal) {
        console.log(`[SUCCESS] ${message}`);
        return;
    }
    Swal.fire({
        toast: true,
        position: 'top-end',
        icon: 'success',
        title: message,
        showConfirmButton: false,
        timer: duration,
        timerProgressBar: true,
    });
};

export const showEntryEditorModal = (options: EntryEditorOptions): Promise<LorebookEntry> => {
    const Swal = window.parent.Swal;
    if (!Swal) {
        return Promise.reject(new Error('Swal not found!'));
    }

    const { entry, isCreating, bookName } = options;
    const title = isCreating ? `在 "${bookName}" 中创建新条目` : `编辑条目: ${entry.comment}`;

    return Swal.fire({
        title: title,
        html: `
            <div style="text-align: left;">
                <label for="swal-comment" class="swal2-label">注释 (条目名)</label>
                <input id="swal-comment" class="swal2-input" value="${escapeHtml(entry.comment || '')}" aria-required="true">

                <label for="swal-keys" class="swal2-label">关键词 (逗号分隔)</label>
                <input id="swal-keys" class="swal2-input" value="${escapeHtml((entry.keys || []).join(', '))}">

                <label for="swal-content" class="swal2-label">内容</label>
                <textarea id="swal-content" class="swal2-textarea">${escapeHtml(entry.content || '')}</textarea>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: '保存',
        cancelButtonText: '取消',
        preConfirm: () => {
            const comment = (document.getElementById('swal-comment') as HTMLInputElement).value;
            const keys = (document.getElementById('swal-keys') as HTMLInputElement).value;
            const content = (document.getElementById('swal-content') as HTMLTextAreaElement).value;

            if (!comment && !keys) {
                Swal.showValidationMessage('注释和关键词不能都为空');
                return false;
            }
            return { comment, keys, content };
        }
    }).then((result: any) => {
        if (result.isConfirmed) {
            const { comment, keys, content } = result.value;
            return {
                ...entry,
                comment,
                keys: keys.split(',').map((k:string) => k.trim()).filter(Boolean),
                content,
            } as LorebookEntry;
        }
        throw new Error('Entry editor cancelled');
    });
};

export const showProgressToast = (initialMessage: string = '正在处理...') => {
    const Swal = window.parent.Swal;
    if (!Swal) {
        console.log(`[PROGRESS] ${initialMessage}`);
        return {
            update: (newMessage: string) => console.log(`[PROGRESS UPDATE] ${newMessage}`),
            remove: () => console.log(`[PROGRESS] Done.`),
        };
    }
    
    Swal.fire({
        toast: true,
        position: 'top-end',
        title: initialMessage,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    return {
        update: (newMessage: string) => {
            const titleElement = Swal.getTitle();
            if (titleElement) {
                titleElement.textContent = newMessage;
            }
        },
        remove: () => Swal.close(),
    };
};

export const showRegexEditorModal = (options: RegexEditorOptions): Promise<TavernRegex> => {
    const Swal = window.parent.Swal;
    if (!Swal) {
        return Promise.reject(new Error('Swal not found!'));
    }

    const { regex, isCreating } = options;
    const title = isCreating ? '创建新正则' : `编辑正则: ${regex.script_name}`;

    return Swal.fire({
        title: title,
        html: `
            <div style="text-align: left;">
                <label for="swal-script-name" class="swal2-label">名称</label>
                <input id="swal-script-name" class="swal2-input" value="${escapeHtml(regex.script_name || '')}" aria-required="true">

                <label for="swal-find-regex" class="swal2-label">查找 (正则表达式)</label>
                <input id="swal-find-regex" class="swal2-input" value="${escapeHtml(regex.find_regex || '')}" aria-required="true">

                <label for="swal-replace-string" class="swal2-label">替换为</label>
                <input id="swal-replace-string" class="swal2-input" value="${escapeHtml(regex.replace_string || '')}">
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: '保存',
        cancelButtonText: '取消',
        preConfirm: () => {
            const script_name = (document.getElementById('swal-script-name') as HTMLInputElement).value;
            const find_regex = (document.getElementById('swal-find-regex') as HTMLInputElement).value;
            const replace_string = (document.getElementById('swal-replace-string') as HTMLInputElement).value;

            if (!script_name || !find_regex) {
                Swal.showValidationMessage('名称和查找正则不能为空');
                return false;
            }
            return { script_name, find_regex, replace_string };
        }
    }).then((result: any) => {
        if (result.isConfirmed) {
            const { script_name, find_regex, replace_string } = result.value;
            return {
                ...regex,
                script_name,
                find_regex,
                replace_string,
            } as TavernRegex;
        }
        throw new Error('Regex editor cancelled');
    });
};