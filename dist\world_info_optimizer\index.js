const e='world-info-optimizer-panel',n='world-info-optimizer-button',o='wio-search-input',t='wio-refresh-btn',r='wio-create-lorebook-btn',a=e=>{'string'!=typeof e&&(e=String(e));const n=document.createElement('p');return n.textContent=e,n.innerHTML},i=(e,n)=>{if(!n||!e)return a(e);const o=a(e),t=a(n).replace(/[.*+?^${}()|[\]\\]/g,'\\$&'),r=new RegExp(`(${t})`,'gi');return o.replace(r,'<mark class="wio-highlight">$1</mark>')},l=e=>{const n=window.parent.Swal;if(!n)return new Promise((n,o)=>{const t=window.parent.prompt(e.text,'string'==typeof e.value?e.value:'');null!==t?n(t):o(new Error('Prompt cancelled'))});switch(e.type||'alert'){case'confirm':return n.fire({title:e.title||'确认',text:e.text,icon:'warning',showCancelButton:!0,confirmButtonText:'确认',cancelButtonText:'取消'}).then(e=>{if(e.isConfirmed)return!0;throw new Error('Confirmation cancelled')});case'prompt':return n.fire({title:e.title,text:e.text,input:'text',inputValue:e.value||'',showCancelButton:!0,confirmButtonText:'确定',cancelButtonText:'取消'}).then(e=>{if(e.isConfirmed&&'string'==typeof e.value)return e.value;throw new Error('Prompt cancelled')});default:return n.fire({title:e.title||'提示',text:e.text,icon:'info'}).then(()=>!0)}},s=e=>{const n=window.parent.Swal;if(!n)return Promise.reject(new Error('Swal not found!'));const{entry:o,isCreating:t,bookName:r}=e,i=t?`在 "${r}" 中创建新条目`:`编辑条目: ${o.comment}`;return n.fire({title:i,html:`\n            <div style="text-align: left;">\n                <label for="swal-comment" class="swal2-label">注释 (条目名)</label>\n                <input id="swal-comment" class="swal2-input" value="${a(o.comment||'')}" aria-required="true">\n\n                <label for="swal-keys" class="swal2-label">关键词 (逗号分隔)</label>\n                <input id="swal-keys" class="swal2-input" value="${a((o.keys||[]).join(', '))}">\n\n                <label for="swal-content" class="swal2-label">内容</label>\n                <textarea id="swal-content" class="swal2-textarea">${a(o.content||'')}</textarea>\n            </div>\n        `,showCancelButton:!0,confirmButtonText:'保存',cancelButtonText:'取消',preConfirm:()=>{const e=document.getElementById('swal-comment').value,o=document.getElementById('swal-keys').value,t=document.getElementById('swal-content').value;return e||o?{comment:e,keys:o,content:t}:(n.showValidationMessage('注释和关键词不能都为空'),!1)}}).then(e=>{if(e.isConfirmed){const{comment:n,keys:t,content:r}=e.value;return{...o,comment:n,keys:t.split(',').map(e=>e.trim()).filter(Boolean),content:r}}throw new Error('Entry editor cancelled')})},c=e=>{const n=window.parent.Swal;if(!n)return Promise.reject(new Error('Swal not found!'));const{regex:o,isCreating:t}=e,r=t?'创建新正则':`编辑正则: ${o.script_name}`;return n.fire({title:r,html:`\n            <div style="text-align: left;">\n                <label for="swal-script-name" class="swal2-label">名称</label>\n                <input id="swal-script-name" class="swal2-input" value="${a(o.script_name||'')}" aria-required="true">\n\n                <label for="swal-find-regex" class="swal2-label">查找 (正则表达式)</label>\n                <input id="swal-find-regex" class="swal2-input" value="${a(o.find_regex||'')}" aria-required="true">\n\n                <label for="swal-replace-string" class="swal2-label">替换为</label>\n                <input id="swal-replace-string" class="swal2-input" value="${a(o.replace_string||'')}">\n            </div>\n        `,showCancelButton:!0,confirmButtonText:'保存',cancelButtonText:'取消',preConfirm:()=>{const e=document.getElementById('swal-script-name').value,o=document.getElementById('swal-find-regex').value,t=document.getElementById('swal-replace-string').value;return e&&o?{script_name:e,find_regex:o,replace_string:t}:(n.showValidationMessage('名称和查找正则不能为空'),!1)}}).then(e=>{if(e.isConfirmed){const{script_name:n,find_regex:t,replace_string:r}=e.value;return{...o,script_name:n,find_regex:t,replace_string:r}}throw new Error('Regex editor cancelled')})},d=(e,n='TavernAPI')=>async(...o)=>{try{return await e(...o)}catch(e){return e&&(console.error(`[${n}] Error:`,e),await l({type:'alert',title:'API调用异常',text:'操作中发生未知错误，请检查开发者控制台获取详细信息。'})),null}};const b=new class{helper;constructor(){this.helper=(()=>{const e=window.parent;if(!e.TavernHelper)throw new Error('TavernHelper is not available on the parent window.');return e.TavernHelper})()}createLorebook=d(async e=>this.helper.createLorebook(e));deleteLorebook=d(async e=>this.helper.deleteLorebook(e));getLorebooks=d(async()=>this.helper.getLorebooks());getLorebookSettings=d(async()=>this.helper.getLorebookSettings());setLorebookSettings=d(async e=>this.helper.setLorebookSettings(e));getLorebookEntries=d(async e=>this.helper.getLorebookEntries(e));setLorebookEntries=d(async(e,n)=>this.helper.setLorebookEntries(e,n));createLorebookEntries=d(async(e,n)=>this.helper.createLorebookEntries(e,n));deleteLorebookEntries=d(async(e,n)=>this.helper.deleteLorebookEntries(e,n));getCharLorebooks=d(async e=>this.helper.getCharLorebooks(e));getCurrentCharLorebooks=d(async()=>this.helper.getCharLorebooks());setCurrentCharLorebooks=d(async e=>this.helper.setCurrentCharLorebooks(e));getChatLorebook=d(async()=>this.helper.getChatLorebook());setChatLorebook=d(async e=>this.helper.setChatLorebook(e));getOrCreateChatLorebook=d(async e=>this.helper.getOrCreateChatLorebook(e));getRegexes=d(async()=>this.helper.getTavernRegexes({scope:'all'}));replaceRegexes=d(async e=>this.helper.replaceTavernRegexes(e,{scope:'all'}));getCharData=d(async()=>this.helper.getCharData());saveSettings=d(async()=>this.helper.builtin.saveSettings());getContext=()=>(()=>{const e=window.parent;return e.SillyTavern&&'function'==typeof e.SillyTavern.getContext?e.SillyTavern.getContext():(console.warn('[WIO API] SillyTavern.getContext is not available.'),{characters:[],characterId:null,chatId:null})})();get Character(){return this.helper.Character}},w={regexes:{global:[],character:[]},lorebooks:{character:[]},chatLorebook:null,allLorebooks:[],lorebookEntries:new Map,lorebookUsage:new Map,activeTab:'global-lore',isDataLoaded:!1,searchFilters:{bookName:!0,entryName:!0,keywords:!0,content:!0},searchQuery:'',multiSelectMode:!1,selectedItems:new Set};let g=JSON.parse(JSON.stringify(w));g.lorebookEntries=new Map,g.lorebookUsage=new Map,g.selectedItems=new Set;const p=[],u=()=>{const e={...g,lorebookEntries:new Map(g.lorebookEntries),lorebookUsage:new Map(g.lorebookUsage),selectedItems:new Set(g.selectedItems)};p.forEach(n=>n(e))},h=()=>({...g,lorebookEntries:new Map(g.lorebookEntries),lorebookUsage:new Map(g.lorebookUsage),selectedItems:new Set(g.selectedItems)}),m=e=>{const n=e(g);g={...g,...n},u()},f=async()=>{m(e=>({...e,isDataLoaded:!1}));try{const n=b.getContext(),{characters:o,characterId:t,chatId:r}=n,a=null!=t,i=null!=r,l=await Promise.allSettled([b.getRegexes(),b.getLorebookSettings(),b.getLorebooks(),a?b.getCharData():Promise.resolve(null),a?b.getCurrentCharLorebooks():Promise.resolve(null),i?b.getChatLorebook():Promise.resolve(null)]),s='fulfilled'===l[0].status?l[0].value:[],c='fulfilled'===l[1].status?l[1].value:{},d='fulfilled'===l[2].status?l[2].value:[],w='fulfilled'===l[3].status?l[3].value:null,g='fulfilled'===l[4].status?l[4].value:null,p='fulfilled'===l[5].status?l[5].value:null,u=(s||[]).filter(e=>'global'===e.scope),h=function(e,n){const o=e?.filter(e=>'character'===e.scope)||[];let t=[];if(n&&b.Character)try{t=(new b.Character(n).getRegexScripts()||[]).map((e,n)=>({id:e.id||`card-${Date.now()}-${n}`,script_name:e.scriptName||'未命名卡内正则',find_regex:e.findRegex,replace_string:e.replaceString,enabled:!e.disabled,scope:'character',source:'card'}))}catch(e){console.warn('[WIO Core] Couldn\'t parse character card regex scripts:',e)}const r=new Set(o.map(e=>`${e.script_name}::${e.find_regex}::${e.replace_string}`)),a=t.filter(e=>{const n=`${e.script_name}::${e.find_regex}::${e.replace_string}`;return!r.has(n)});return[...o,...a]}(s,w),f=new Set(c?.selected_global_lorebooks||[]),x=(d||[]).map(e=>({name:e,enabled:f.has(e)})),v=new Set;g?.primary&&v.add(g.primary),g?.additional&&g.additional.forEach(e=>v.add(e));const y=Array.from(v),k=new Map,L=new Set(d||[]);if(Array.isArray(o))for(const e of o){if(!e?.name)continue;const n=await b.getCharLorebooks({name:e.name}),o=new Set;n?.primary&&o.add(n.primary),n?.additional&&n.additional.forEach(e=>o.add(e)),o.forEach(n=>{k.has(n)||k.set(n,[]),k.get(n)?.push(e.name),L.add(n)})}y.forEach(e=>L.add(e)),p&&L.add(p);const C=new Map,E=Array.from(L).map(async e=>{const n=await b.getLorebookEntries(e);n&&C.set(e,n)});await Promise.all(E),e={globalRegexes:u,characterRegexes:h,allLorebooks:x,characterLorebooks:y,chatLorebook:p||null,lorebookEntries:C,lorebookUsage:k},m(n=>({...n,regexes:{global:e.globalRegexes,character:e.characterRegexes},allLorebooks:e.allLorebooks,lorebooks:{character:e.characterLorebooks},chatLorebook:e.chatLorebook,lorebookEntries:e.lorebookEntries,lorebookUsage:e.lorebookUsage,isDataLoaded:!0}))}catch(e){console.error('[WIO Core] Failed to load all data:',e),m(e=>({...e,isDataLoaded:!0}))}var e};const x=async e=>{null!==await b.replaceRegexes(e)&&await f()};let v,$;const y=a=>{v=a.document,$=a.jQuery;const i=$('body',v);i.on('click',`#${n}`,()=>{$(`#${e}`,v).fadeIn(200),h().isDataLoaded||f()}),i.on('click',`#${e} .wio-close-btn`,()=>{$(`#${e}`,v).fadeOut(200)});const l=$(`#${e}`,v);l.on('click',async e=>{const n=$(e.target),o=n.closest('.wio-tab-btn');if(o.length){return void(e=>{m(n=>({...n,activeTab:e}))})(o.data('tab-id'))}if(n.closest(`#${r}`).length)return void S();if(n.closest(`#${t}`).length)return void f();const a=n.closest('.wio-rename-book-btn');if(a.length){const e=a.closest('.wio-book-group').data('book-name');return void k(e)}const i=n.closest('.wio-delete-book-btn');if(i.length){const e=i.closest('.wio-book-group').data('book-name');return void L(e)}const l=n.closest('.wio-create-entry-btn');if(l.length){const e=l.data('book-name');return void C(e)}const s=n.closest('.wio-edit-entry-btn');if(s.length){const e=s.closest('.wio-entry-item'),n=e.data('book-name'),o=e.data('uid');return void E(n,o)}const c=n.closest('.wio-delete-entry-btn');if(c.length){const e=c.closest('.wio-entry-item'),n=e.data('book-name'),o=e.data('uid');return void I(n,o)}const d=n.closest('.wio-create-regex-btn');if(d.length){const e=d.data('scope');return void T(e)}const b=n.closest('.wio-edit-regex-btn');if(b.length){const e=b.closest('.wio-regex-item').data('id');return void O(e)}const w=n.closest('.wio-delete-regex-btn');if(w.length){const e=w.closest('.wio-regex-item').data('id');return void j(e)}}),l.on('change',async e=>{const n=$(e.target);if(n.is('.wio-global-book-toggle')){const e=n.closest('.wio-book-group').data('book-name'),o=n.prop('checked');return void await(async(e,n)=>{const o=await b.getLorebookSettings()||{};o.selected_global_lorebooks||(o.selected_global_lorebooks=[]);const t=new Set(o.selected_global_lorebooks);if(n?t.add(e):t.delete(e),o.selected_global_lorebooks=Array.from(t),null!==await b.setLorebookSettings(o)){const o=h(),t=o.allLorebooks.find(n=>n.name===e);t&&(t.enabled=n,m(e=>({...e,allLorebooks:[...o.allLorebooks]})))}})(e,o)}}),l.on('input',`#${o}`,e=>{(e=>{m(n=>({...n,searchQuery:e}))})($(e.target).val())})},k=async e=>{try{const n=await l({type:'prompt',title:'重命名世界书',text:`为 "${e}" 输入新的名称:`,value:e});'string'==typeof n&&n.trim()&&n!==e&&await(async(e,n)=>{const o=await b.getLorebookEntries(e);if(null===o)throw new Error(`Failed to get entries for lorebook: ${e}`);if(null===await b.createLorebook(n))throw new Error(`Failed to create new lorebook: ${n}`);if(o.length>0&&null===await b.setLorebookEntries(n,o))throw await b.deleteLorebook(n),new Error(`Failed to set entries for new lorebook: ${n}`);null===await b.deleteLorebook(e)&&console.warn(`Failed to delete old lorebook "${e}" after renaming.`),await f()})(e,n.trim())}catch(e){console.log('Rename operation cancelled.')}},L=async e=>{try{await l({type:'confirm',title:'确认删除',text:`你确定要永久删除世界书 "${e}" 吗？此操作无法撤销。`})&&await(async e=>{null!==await b.deleteLorebook(e)&&await f()})(e)}catch(e){console.log('Delete operation cancelled.')}},C=async e=>{try{const n={...await s({entry:{keys:[],content:'',comment:''},bookName:e,isCreating:!0}),enabled:!0};await(async(e,n)=>{const o=await b.createLorebookEntries(e,[n]);if(null!==o&&o.length>0){const n=h(),t=o[0],r=n.lorebookEntries.get(e)||[];n.lorebookEntries.set(e,[...r,t]),m(e=>({...e,lorebookEntries:new Map(n.lorebookEntries)}))}})(e,n)}catch(e){console.log('Create entry operation cancelled.')}},E=async(e,n)=>{const o=h(),t=o.lorebookEntries.get(e)?.find(e=>e.uid===n);if(t)try{const o=await s({entry:{...t},bookName:e,isCreating:!1}),r={comment:o.comment,keys:o.keys,content:o.content};await(async(e,n,o)=>{const t=h(),r=t.lorebookEntries.get(e);if(!r)throw new Error(`[WIO Core] Book not found in state: ${e}`);let a=!1;const i=r.map(e=>e.uid===n?(a=!0,{...e,...o}):e);a&&null!==await b.setLorebookEntries(e,i)&&(t.lorebookEntries.set(e,i),m(e=>({...e,lorebookEntries:new Map(t.lorebookEntries)})))})(e,n,r)}catch(e){console.log('Edit entry operation cancelled.')}else console.error(`Entry with UID ${n} not found in book ${e}.`)},I=async(e,n)=>{try{await l({type:'confirm',title:'确认删除',text:'你确定要永久删除这个条目吗？'})&&await(async(e,n)=>{if(null!==await b.deleteLorebookEntries(e,[n])){const o=h(),t=(o.lorebookEntries.get(e)||[]).filter(e=>e.uid!==n);o.lorebookEntries.set(e,t),m(e=>({...e,lorebookEntries:new Map(o.lorebookEntries)}))}})(e,n)}catch(e){console.log('Delete entry operation cancelled.')}},S=async()=>{try{const e=await l({type:'prompt',title:'创建新世界书',text:'请输入新世界书的名称:',value:'New-Lorebook'});'string'==typeof e&&e.trim()&&await(async e=>{null!==await b.createLorebook(e)&&await f()})(e.trim())}catch(e){console.log('Create lorebook operation cancelled.')}},T=async e=>{try{const n=await c({regex:{script_name:'新正则',find_regex:'',replace_string:''},isCreating:!0});await(async e=>{const n=h(),o=[...n.regexes.global.filter(e=>'card'!==e.source),...n.regexes.character.filter(e=>'card'!==e.source)],t={id:`ui-${Date.now()}`,enabled:!0,source:'ui',...e};await x([...o,t])})({...n,scope:e})}catch(e){console.log('Create regex operation cancelled.')}},O=async e=>{const n=h(),o=[...n.regexes.global,...n.regexes.character].find(n=>n.id===e);if(o)if('card'!==o.source)try{const n=await c({regex:{...o},isCreating:!1});await(async(e,n)=>{const o=h(),t=[...o.regexes.global.filter(e=>'card'!==e.source),...o.regexes.character.filter(e=>'card'!==e.source)].map(o=>o.id===e?{...o,...n}:o);await x(t)})(e,n)}catch(e){console.log('Edit regex operation cancelled.')}else await l({type:'alert',title:'操作无效',text:'无法编辑来自角色卡的正则表达式。'});else console.error(`Regex with ID ${e} not found.`)},j=async e=>{const n=h(),o=[...n.regexes.global,...n.regexes.character].find(n=>n.id===e);if(o&&'card'===o.source)await l({type:'alert',title:'操作无效',text:'无法删除来自角色卡的正则表达式。'});else try{await l({type:'confirm',title:'确认删除',text:'你确定要永久删除这个正则表达式吗？'})&&await(async e=>{const n=h(),o=[...n.regexes.global.filter(e=>'card'!==e.source),...n.regexes.character.filter(e=>'card'!==e.source)].filter(n=>n.id!==e);await x(o)})(e)}catch(e){console.log('Delete regex operation cancelled.')}},M=(e,n,o,t=!0,r)=>{const l=void 0!==r?r:n.lorebookEntries.get(e.name)||[],s=n.lorebookEntries.get(e.name)?.length||0,c=n.lorebookUsage.get(e.name)||[],d=o&&l.length!==s?`${l.length} / ${s}`:`${s}`;return`\n        <div class="wio-book-group" data-book-name="${a(e.name)}">\n            <div class="wio-book-header">\n                ${t?`<input type="checkbox" ${e.enabled?'checked':''} class="wio-global-book-toggle" aria-label="启用 ${a(e.name)}">`:''}\n                <h4>${i(e.name,o)} <span class="wio-entry-count">(${d})</span></h4>\n                ${c.length>0?`<span class="wio-usage-pill" title="被 ${c.join(', ')} 使用" aria-label="被 ${c.length} 个角色使用">${c.length}</span>`:''}\n                <div class="wio-item-controls">\n                    <button class="wio-rename-book-btn" title="重命名" aria-label="重命名 ${a(e.name)}"><i class="fa-solid fa-pencil"></i></button>\n                    <button class="wio-delete-book-btn" title="删除" aria-label="删除 ${a(e.name)}"><i class="fa-solid fa-trash-can"></i></button>\n                </div>\n            </div>\n            <div class="wio-entry-list">\n                ${l.map(n=>W(n,e.name,o)).join('')}\n                <div class="wio-entry-actions"><button class="wio-create-entry-btn" data-book-name="${a(e.name)}" aria-label="在 ${a(e.name)} 中新建条目">+ 新建条目</button></div>\n            </div>\n        </div>\n    `},W=(e,n,o)=>`\n        <div class="wio-entry-item" data-uid="${e.uid}" data-book-name="${a(n)}">\n            <div class="wio-entry-main">\n                <input type="checkbox" ${e.enabled?'checked':''} class="wio-entry-toggle" aria-label="启用 ${a(displayName)}">\n                <span class="wio-entry-name">${i(displayName,o)}</span>\n                <span class="wio-entry-keys">${i((e.keys||[]).join(', '),o)}</span>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-entry-btn" title="编辑" aria-label="编辑 ${a(displayName)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-entry-btn" title="删除" aria-label="删除 ${a(displayName)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `,B=(e,n,o,t)=>{let r=`<div class="wio-regex-group" data-scope="${t}"><h3>${o} (${e.length})</h3>`;0!==e.length||'character'!==t||h().character?0===e.length&&(r+='<p class="wio-info-text">没有找到正则。</p>'):r+='<p class="wio-info-text">没有加载角色，无法显示角色正则。</p>';const l=n?e.filter(e=>(e.script_name||'').toLowerCase().includes(n)||(e.find_regex||'').toLowerCase().includes(n)||(e.replace_string||'').toLowerCase().includes(n)):e;return l.length>0?r+=l.map(e=>((e,n)=>{const o=e.id||`${e.scope}-${btoa(unescape(encodeURIComponent(e.script_name+e.find_regex)))}`;return`\n        <div class="wio-regex-item" data-id="${a(o)}" data-scope="${e.scope}">\n            <div class="wio-regex-main">\n                <input type="checkbox" ${e.enabled?'checked':''} class="wio-regex-toggle" aria-label="启用 ${a(displayName)}">\n                <span class="wio-regex-name">${i(displayName,n)} ${'card'===e.source?'<span class="wio-regex-source-badge" aria-label="卡内正则">(卡)</span>':''}</span>\n                <code class="wio-regex-find" title="查找: ${a(e.find_regex)}">${i(e.find_regex,n)}</code>\n                <i class="fa-solid fa-arrow-right-long" aria-hidden="true"></i>\n                <code class="wio-regex-replace" title="替换为: ${a(e.replace_string)}">${i(e.replace_string,n)}</code>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-regex-btn" title="编辑" aria-label="编辑 ${a(displayName)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-regex-btn" title="删除" aria-label="删除 ${a(displayName)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `})(e,n)).join(''):n&&(r+=`<p class="wio-info-text">没有找到与 "${a(n)}" 匹配的正则。</p>`),('global'===t||'character'===t&&h().character)&&(r+=`<div class="wio-regex-actions"><button class="wio-create-regex-btn" data-scope="${t}">+ 新建正则</button></div>`),r+'</div>'};let D,R;const N=()=>{if(!D)return;const n=h(),t=R(`#${e}`,D);if(!t.length)return;t.find('.wio-tab-btn').removeClass('active'),t.find(`.wio-tab-btn[data-tab-id="${n.activeTab}"]`).addClass('active');const r=t.find('.wio-main-content');if(!n.isDataLoaded)return void r.html('<p class="wio-info-text">正在加载数据...</p>');const i=n.searchQuery.toLowerCase(),l=R(`#${o}`,D);switch(l.val()!==n.searchQuery&&l.val(n.searchQuery),n.activeTab){case'global-lore':r.html(((e,n)=>{const o=[...e.allLorebooks].sort((e,n)=>(n.enabled?1:-1)-(e.enabled?1:-1)||e.name.localeCompare(n.name));return 0===o.length?'<p class="wio-info-text">没有找到全局世界书。</p>':n?o.map(o=>{const t=o.name.toLowerCase().includes(n),r=e.lorebookEntries.get(o.name)||[],a=r.filter(e=>(e.comment||'').toLowerCase().includes(n)||(e.keys||[]).join(', ').toLowerCase().includes(n)||(e.content||'').toLowerCase().includes(n));return t||a.length>0?M(o,e,n,!0,t?r:a):''}).join('')||`<p class="wio-info-text">没有找到与 "${a(n)}" 匹配的结果。</p>`:o.map(o=>M(o,e,n)).join('')})(n,i));break;case'char-lore':r.html(((e,n)=>{const o=e.lorebooks.character;return 0===o.length?'<p class="wio-info-text">当前角色没有绑定的世界书。</p>':n?o.map(o=>{const t=e.allLorebooks.find(e=>e.name===o)||{name:o,enabled:!1},r=t.name.toLowerCase().includes(n),a=e.lorebookEntries.get(t.name)||[],i=a.filter(e=>(e.comment||'').toLowerCase().includes(n)||(e.keys||[]).join(', ').toLowerCase().includes(n)||(e.content||'').toLowerCase().includes(n));return r||i.length>0?M(t,e,n,!1,r?a:i):''}).join('')||`<p class="wio-info-text">没有找到与 "${a(n)}" 匹配的结果。</p>`:o.map(o=>{const t=e.allLorebooks.find(e=>e.name===o)||{name:o,enabled:!1};return M(t,e,n,!1)}).join('')})(n,i));break;case'chat-lore':r.html(((e,n)=>{const o=e.chatLorebook;if(!o)return'<p class="wio-info-text">当前聊天没有绑定的世界书。</p>';const t=e.allLorebooks.find(e=>e.name===o)||{name:o,enabled:!1};if(!n)return M(t,e,n,!1);const r=t.name.toLowerCase().includes(n),i=e.lorebookEntries.get(t.name)||[],l=i.filter(e=>(e.comment||'').toLowerCase().includes(n)||(e.keys||[]).join(', ').toLowerCase().includes(n)||(e.content||'').toLowerCase().includes(n));if(r||l.length>0)return M(t,e,n,!1,r?i:l);return`<p class="wio-info-text">没有找到与 "${a(n)}" 匹配的结果。</p>`})(n,i));break;case'global-regex':r.html(B(n.regexes.global,i,'全局正则','global'));break;case'char-regex':r.html(B(n.regexes.character,i,'角色正则','character'));break;default:r.html(`<p>未知视图: ${n.activeTab}</p>`)}},A=()=>{var e;e=N,p.push(e)};function P(a){console.log('[WIO] Initializing World Info Optimizer...'),(a=>{if(D=a.document,R=a.jQuery,R(`#${n}`,D).length>0)return void console.log('[WIO] UI already injected.');const i=`\n        /* All WIO styles here... */\n        #${e} {\n            display: none;\n            position: fixed;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            z-index: 10000;\n            overflow: hidden;\n        }\n        \n        #${e} .wio-panel-inner {\n            display: flex;\n            flex-direction: column;\n            height: 100vh;\n            width: 100vw;\n            background-color: var(--wio-bg-primary);\n            color: var(--wio-text-primary);\n        }\n\n        /* 响应式面板调整 */\n        @media (min-width: 768px) {\n            #${e} {\n                top: 50%;\n                left: 50%;\n                right: auto;\n                bottom: auto;\n                transform: translate(-50%, -50%);\n                width: 80%;\n                max-width: 900px;\n                height: 70%;\n                border: 1px solid var(--wio-border-color);\n                border-radius: var(--wio-border-radius);\n                box-shadow: var(--wio-shadow);\n            }\n            \n            #${e} .wio-panel-inner {\n                height: 100%;\n                width: 100%;\n            }\n        }\n\n        /* 头部样式 */\n        .wio-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-secondary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        .wio-header h2 {\n            margin: 0;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-close-btn {\n            background: none;\n            border: none;\n            color: var(--wio-text-primary);\n            font-size: 24px;\n            cursor: pointer;\n            padding: var(--wio-spacing-xs);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }        \n        \n        /* 选项卡样式 */\n        .wio-tabs {\n            display: flex;\n            background-color: var(--wio-bg-tertiary);\n            overflow-x: auto;\n            white-space: nowrap;\n            flex-shrink: 0;\n            border-bottom: 1px solid var(--wio-border-color);\n            -ms-overflow-style: none;\n            scrollbar-width: none;\n        }\n        \n        .wio-tabs::-webkit-scrollbar {\n            display: none;\n        }\n        \n        .wio-tab-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: none;\n            background-color: transparent;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            border-bottom: 2px solid transparent;\n            flex-shrink: 0;\n            font-size: var(--wio-font-size-md);\n            transition: all 0.2s ease;\n            outline: none;\n        }\n        \n        .wio-tab-btn.active {\n            color: var(--wio-text-primary);\n            border-bottom-color: var(--wio-highlight-color);\n            background-color: rgba(0, 170, 255, 0.1);\n        }\n        \n        .wio-tab-btn:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n        \n        /* 工具栏样式 */\n        .wio-toolbar {\n            padding: var(--wio-spacing-md);\n            display: flex;\n            gap: var(--wio-spacing-md);\n            background-color: var(--wio-bg-toolbar);\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        #${o} {\n            flex-grow: 1;\n            padding: var(--wio-spacing-md);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-primary);\n            color: var(--wio-text-primary);\n            font-size: var(--wio-font-size-md);\n            outline: none;\n        }\n        \n        #${o}::placeholder {\n            color: var(--wio-text-secondary);\n        }\n        \n        #${o}:focus {\n            border-color: var(--wio-highlight-color);\n            box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.2);\n        }\n        \n        .wio-toolbar button {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-secondary);\n            color: var(--wio-text-primary);\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: var(--wio-spacing-xs);\n            transition: all 0.2s ease;\n            outline: none;\n            min-width: 44px;\n            min-height: 44px;\n        }\n        \n        .wio-toolbar button:hover {\n            background-color: var(--wio-highlight-color);\n            border-color: var(--wio-highlight-color);\n        }\n        \n        .wio-toolbar button:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 主内容区域样式 */\n        .wio-main-content {\n            flex-grow: 1;\n            overflow-y: auto;\n            padding: var(--wio-spacing-lg);\n        }\n        \n        /* 列表样式 */\n        .wio-book-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n        }\n        \n        .wio-book-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-secondary);\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-border-color);\n        }\n        \n        .wio-book-header h4 {\n            margin: 0;\n            flex-grow: 1;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-usage-pill {\n            padding: 2px 8px;\n            background-color: var(--wio-highlight-color);\n            color: white;\n            border-radius: 12px;\n            font-size: var(--wio-font-size-sm);\n        }\n        \n        .wio-item-controls {\n            display: flex;\n            gap: var(--wio-spacing-xs);\n        }\n        \n        .wio-item-controls button {\n            padding: var(--wio-spacing-xs) var(--wio-spacing-sm);\n            background-color: transparent;\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            color: var(--wio-text-primary);\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n        \n        .wio-item-controls button:hover {\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-entry-list {\n            background-color: var(--wio-bg-primary);\n        }\n        \n        .wio-entry-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-border-color);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-entry-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-entry-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n        }\n        \n        .wio-entry-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-entry-keys {\n            font-size: var(--wio-font-size-sm);\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            flex-grow: 1;\n            word-break: break-word;\n        }\n        \n        .wio-entry-actions,\n        .wio-regex-actions {\n            padding: var(--wio-spacing-md);\n            text-align: center;\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-create-entry-btn,\n        .wio-create-regex-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-highlight-color);\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: white;\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n        }\n\n        /* 正则表达式样式 */\n        .wio-regex-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n        }\n        \n        .wio-regex-group h3 {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            margin: 0;\n            background-color: var(--wio-bg-secondary);\n            border-bottom: 1px solid var(--wio-border-color);\n        }\n        \n        .wio-regex-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-border-color);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-regex-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-regex-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n            flex-wrap: wrap;\n        }\n        \n        .wio-regex-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-regex-find,\n        .wio-regex-replace {\n            background-color: var(--wio-bg-tertiary);\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-family: monospace;\n            font-size: var(--wio-font-size-sm);\n            word-break: break-all;\n        }\n        \n        .wio-info-text {\n            text-align: center;\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            padding: var(--wio-spacing-lg);\n        }\n        \n        .wio-search-highlight {\n            background-color: rgba(255, 255, 0, 0.3);\n            padding: 0 2px;\n            border-radius: 2px;\n        }\n\n        /* 页脚样式 */\n        .wio-footer {\n            padding: var(--wio-spacing-sm) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-tertiary);\n            font-size: var(--wio-font-size-sm);\n            text-align: right;\n            border-top: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n\n        /* 复选框样式优化 */\n        input[type="checkbox"] {\n            transform: scale(1.2);\n            accent-color: var(--wio-highlight-color);\n            margin-right: var(--wio-spacing-xs);\n        }\n        \n        /* 聚焦样式优化 */\n        :focus-visible {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 滚动条样式 */\n        .wio-main-content::-webkit-scrollbar,\n        .wio-tabs::-webkit-scrollbar {\n            width: 8px;\n            height: 8px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-track,\n        .wio-tabs::-webkit-scrollbar-track {\n            background: var(--wio-bg-tertiary);\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb,\n        .wio-tabs::-webkit-scrollbar-thumb {\n            background: var(--wio-border-color);\n            border-radius: 4px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb:hover,\n        .wio-tabs::-webkit-scrollbar-thumb:hover {\n            background: #777;\n        }\n\n        /* 媒体查询：小型设备优化 */\n        @media (max-width: 767px) {\n            :root {\n                --wio-spacing-xs: 2px;\n                --wio-spacing-sm: 6px;\n                --wio-spacing-md: 8px;\n                --wio-spacing-lg: 10px;\n                --wio-font-size-sm: 11px;\n                --wio-font-size-md: 13px;\n                --wio-font-size-lg: 15px;\n            }\n            \n            .wio-header h2 {\n                font-size: var(--wio-font-size-md);\n            }\n            \n            .wio-entry-main {\n                flex-wrap: wrap;\n            }\n            \n            .wio-entry-name {\n                flex-basis: 100%;\n                margin-bottom: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-main {\n                flex-direction: column;\n                align-items: flex-start;\n                gap: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-name,\n            .wio-regex-find,\n            .wio-regex-replace {\n                width: 100%;\n                box-sizing: border-box;\n            }\n            \n            .wio-toolbar {\n                flex-wrap: wrap;\n            }\n            \n            #${o} {\n                width: 100%;\n            }\n            \n            /* 触摸目标优化 */\n            button,\n            input[type="checkbox"] {\n                touch-action: manipulation;\n            }\n        }\n        \n        /* 平板设备优化 */\n        @media (min-width: 768px) and (max-width: 1024px) {\n            #${e} {\n                width: 90%;\n                height: 80%;\n            }\n        }\n\n        /* 高对比度模式支持 */\n        @media (prefers-contrast: high) {\n            :root {\n                --wio-border-color: #fff;\n                --wio-bg-primary: #000;\n                --wio-bg-secondary: #333;\n                --wio-bg-tertiary: #222;\n                --wio-bg-toolbar: #444;\n                --wio-text-primary: #fff;\n                --wio-text-secondary: #ddd;\n                --wio-highlight-color: #ff0;\n            }\n        }\n\n        /* 减少动画模式支持 */\n        @media (prefers-reduced-motion: reduce) {\n            * {\n                animation-duration: 0.01ms !important;\n                animation-iteration-count: 1 !important;\n                transition-duration: 0.01ms !important;\n            }\n        }\n    `,l=D.createElement('style');l.type='text/css',l.innerText=i,D.head.appendChild(l);const s=`\n        <div id="${e}">\n            <div class="wio-panel-inner">\n                <div class="wio-header">\n                    <h2>世界书 & 正则便捷管理 (WIO)</h2>\n                    <button class="wio-close-btn" aria-label="关闭面板">&times;</button>\n                </div>\n                <div class="wio-tabs" role="tablist">\n                    <button class="wio-tab-btn" data-tab-id="global-lore" role="tab" aria-controls="global-lore-content" aria-selected="false">全局世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="char-lore" role="tab" aria-controls="char-lore-content" aria-selected="false">角色世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="chat-lore" role="tab" aria-controls="chat-lore-content" aria-selected="false">聊天世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="global-regex" role="tab" aria-controls="global-regex-content" aria-selected="false">全局正则</button>\n                    <button class="wio-tab-btn" data-tab-id="char-regex" role="tab" aria-controls="char-regex-content" aria-selected="false">角色正则</button>\n                </div>\n                <div class="wio-toolbar">\n                    <input type="search" id="${o}" placeholder="搜索..." aria-label="搜索内容">\n                    <button id="${t}" title="刷新数据" aria-label="刷新数据"><i class="fa-solid fa-sync"></i></button>\n                    <button id="${r}" title="新建世界书" aria-label="新建世界书"><i class="fa-solid fa-plus"></i></button>\n                </div>\n                <div class="wio-main-content" role="main" id="tab-content-container"></div>\n                <div class="wio-footer">\n                    <span>WIO v3.0 (Refactored)</span>\n                </div>\n            </div>\n        </div>\n    `;R('body',D).append(s);const c=`\n        <div id="${n}" class="list-group-item">\n            <img src="https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png" style="width: 24px; height: 24px; margin-right: 10px;">\n            <span>世界书 & 正则便捷管理 (WIO)</span>\n        </div>\n    `;R('#extensions_list',D).append(c),console.log('[WIO] UI Injected successfully.')})(a),A(),y(a),console.log('[WIO] World Info Optimizer initialized successfully.')}console.log('[WIO Script] Execution started.'),function(e){const n='#extensionsMenu';let o=0;console.log('[WIO] Starting readiness check...');const t=setInterval(()=>{const r=window.parent;if(!r)return void o++;const a=null!==r.document.querySelector(n),i=r.TavernHelper&&'function'==typeof r.TavernHelper.getCharData&&r.jQuery;if(a&&i){clearInterval(t),console.log('[WIO] SUCCESS: DOM and Core APIs are ready. Initializing script.');try{e(r)}catch(e){console.error('[WIO] FATAL: Error during main callback execution.',e)}}else o++,o>100&&(clearInterval(t),console.error('[WIO] FATAL: Readiness check timed out.'),a||console.error(`[WIO] -> Failure: DOM element "${n}" not found.`),i||console.error(`[WIO] -> Failure: Core APIs not available. TavernHelper: ${!!r.TavernHelper}, jQuery: ${!!r.jQuery}`))},200)}(P);