var __webpack_modules__ = {
  "./src/world_info_optimizer/api.ts": 
  /*!*****************************************!*\
  !*** ./src/world_info_optimizer/api.ts ***!
  \*****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TavernAPI: () => (/* binding */ TavernAPI)\n/* harmony export */ });\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ui */ \"./src/world_info_optimizer/ui.ts\");\n// src/world_info_optimizer/api.ts\n // 假设的UI模块\n/**\n * 错误处理装饰器或高阶函数，用于封装API调用。\n * @param fn 要封装的异步函数\n * @param context 错误日志的上下文\n */\nconst errorCatched = (fn, context = 'TavernAPI') => {\n    return async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            // 用户取消操作（例如在模态框中点击取消）通常会抛出null或undefined\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                // 可以在这里决定是否显示一个全局的错误提示\n                await (0,_ui__WEBPACK_IMPORTED_MODULE_0__.showModal)({\n                    type: 'alert',\n                    title: 'API调用异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n            return null;\n        }\n    };\n};\n/**\n * 获取全局TavernHelper实例。\n * @returns {TavernHelper}\n * @throws 如果TavernHelper未定义，则抛出错误。\n */\nconst getTavernHelper = () => {\n    const parentWin = window.parent;\n    if (!parentWin.TavernHelper) {\n        throw new Error('TavernHelper is not available on the parent window.');\n    }\n    return parentWin.TavernHelper;\n};\nconst getSillyTavernContext = () => {\n    const parentWin = window.parent;\n    if (!parentWin.SillyTavern || typeof parentWin.SillyTavern.getContext !== 'function') {\n        // 在这种情况下，我们不应该抛出错误，而应该返回一个默认的、无害的上下文。\n        // 因为脚本可能在SillyTavern完全加载之前运行。\n        console.warn('[WIO API] SillyTavern.getContext is not available.');\n        return {\n            characters: [],\n            characterId: null,\n            chatId: null,\n        };\n    }\n    return parentWin.SillyTavern.getContext();\n};\n// API封装模块\nclass TavernAPIWrapper {\n    helper;\n    constructor() {\n        this.helper = getTavernHelper();\n    }\n    // Lorebook APIs\n    createLorebook = errorCatched(async (name) => this.helper.createLorebook(name));\n    deleteLorebook = errorCatched(async (name) => this.helper.deleteLorebook(name));\n    getLorebooks = errorCatched(async () => this.helper.getLorebooks());\n    getLorebookSettings = errorCatched(async () => this.helper.getLorebookSettings());\n    setLorebookSettings = errorCatched(async (settings) => this.helper.setLorebookSettings(settings));\n    // Lorebook Entry APIs\n    getLorebookEntries = errorCatched(async (name) => this.helper.getLorebookEntries(name));\n    setLorebookEntries = errorCatched(async (name, entries) => this.helper.setLorebookEntries(name, entries));\n    createLorebookEntries = errorCatched(async (name, entries) => this.helper.createLorebookEntries(name, entries));\n    deleteLorebookEntries = errorCatched(async (name, uids) => this.helper.deleteLorebookEntries(name, uids));\n    // Character-specific Lorebook APIs\n    getCharLorebooks = errorCatched(async (charData) => this.helper.getCharLorebooks(charData));\n    getCurrentCharLorebooks = errorCatched(async () => this.helper.getCharLorebooks());\n    setCurrentCharLorebooks = errorCatched(async (lorebooks) => this.helper.setCurrentCharLorebooks(lorebooks));\n    // Chat Lorebook APIs\n    getChatLorebook = errorCatched(async () => this.helper.getChatLorebook());\n    setChatLorebook = errorCatched(async (name) => this.helper.setChatLorebook(name));\n    getOrCreateChatLorebook = errorCatched(async (name) => this.helper.getOrCreateChatLorebook(name));\n    // Regex APIs\n    getRegexes = errorCatched(async () => this.helper.getTavernRegexes({ scope: 'all' }));\n    replaceRegexes = errorCatched(async (regexes) => this.helper.replaceTavernRegexes(regexes, { scope: 'all' }));\n    // Character Data APIs\n    getCharData = errorCatched(async () => this.helper.getCharData());\n    // Misc APIs\n    saveSettings = errorCatched(async () => this.helper.builtin.saveSettings());\n    // Context API\n    getContext = () => getSillyTavernContext();\n    // Direct access to specific properties if needed\n    get Character() {\n        return this.helper.Character;\n    }\n}\n// 导出单例\nconst TavernAPI = new TavernAPIWrapper();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/api.ts\n\n}");
  },
  "./src/world_info_optimizer/constants.ts": 
  /*!***********************************************!*\
  !*** ./src/world_info_optimizer/constants.ts ***!
  \***********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUTTON_ICON_URL: () => (/* binding */ BUTTON_ICON_URL),\n/* harmony export */   BUTTON_ID: () => (/* binding */ BUTTON_ID),\n/* harmony export */   BUTTON_TEXT_IN_MENU: () => (/* binding */ BUTTON_TEXT_IN_MENU),\n/* harmony export */   BUTTON_TOOLTIP: () => (/* binding */ BUTTON_TOOLTIP),\n/* harmony export */   COLLAPSE_ALL_BTN_ID: () => (/* binding */ COLLAPSE_ALL_BTN_ID),\n/* harmony export */   COLLAPSE_CURRENT_BTN_ID: () => (/* binding */ COLLAPSE_CURRENT_BTN_ID),\n/* harmony export */   CREATE_LOREBOOK_BTN_ID: () => (/* binding */ CREATE_LOREBOOK_BTN_ID),\n/* harmony export */   INITIAL_ACTIVE_TAB: () => (/* binding */ INITIAL_ACTIVE_TAB),\n/* harmony export */   LOREBOOK_INSERTION_POSITIONS: () => (/* binding */ LOREBOOK_INSERTION_POSITIONS),\n/* harmony export */   LOREBOOK_LOGIC_OPTIONS: () => (/* binding */ LOREBOOK_LOGIC_OPTIONS),\n/* harmony export */   PANEL_ID: () => (/* binding */ PANEL_ID),\n/* harmony export */   REFRESH_BTN_ID: () => (/* binding */ REFRESH_BTN_ID),\n/* harmony export */   SEARCH_INPUT_ID: () => (/* binding */ SEARCH_INPUT_ID)\n/* harmony export */ });\n// src/world_info_optimizer/constants.ts\n// --- UI Element IDs ---\nconst PANEL_ID = 'world-info-optimizer-panel';\nconst BUTTON_ID = 'world-info-optimizer-button';\nconst SEARCH_INPUT_ID = 'wio-search-input';\nconst REFRESH_BTN_ID = 'wio-refresh-btn';\nconst COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\nconst COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\nconst CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n// --- UI Display Text & URLs ---\nconst BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\nconst BUTTON_TOOLTIP = '世界书 & 正则便捷管理 (WIO)';\nconst BUTTON_TEXT_IN_MENU = '世界书 & 正则便捷管理 (WIO)';\n// --- Lorebook Configuration Options ---\nconst LOREBOOK_INSERTION_POSITIONS = {\n    'before_character_definition': '角色定义前',\n    'after_character_definition': '角色定义后',\n    'before_example_messages': '聊天示例前',\n    'after_example_messages': '聊天示例后',\n    'before_author_note': '作者笔记前',\n    'after_author_note': '作者笔记后',\n    'at_depth_as_system': '@D ⚙ 系统',\n    'at_depth_as_assistant': '@D 🗨️ 角色',\n    'at_depth_as_user': '@D 👤 用户'\n};\nconst LOREBOOK_LOGIC_OPTIONS = {\n    'and_any': '任一 AND',\n    'and_all': '所有 AND',\n    'not_any': '任一 NOT',\n    'not_all': '所有 NOT'\n};\n// --- Other Constants ---\nconst INITIAL_ACTIVE_TAB = 'global-lore';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/constants.ts\n\n}");
  },
  "./src/world_info_optimizer/core.ts": 
  /*!******************************************!*\
  !*** ./src/world_info_optimizer/core.ts ***!
  \******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLorebook: () => (/* binding */ createLorebook),\n/* harmony export */   createLorebookEntry: () => (/* binding */ createLorebookEntry),\n/* harmony export */   createRegex: () => (/* binding */ createRegex),\n/* harmony export */   deleteLorebook: () => (/* binding */ deleteLorebook),\n/* harmony export */   deleteLorebookEntry: () => (/* binding */ deleteLorebookEntry),\n/* harmony export */   deleteRegex: () => (/* binding */ deleteRegex),\n/* harmony export */   loadAllData: () => (/* binding */ loadAllData),\n/* harmony export */   renameLorebook: () => (/* binding */ renameLorebook),\n/* harmony export */   setGlobalLorebookEnabled: () => (/* binding */ setGlobalLorebookEnabled),\n/* harmony export */   updateLorebookEntry: () => (/* binding */ updateLorebookEntry),\n/* harmony export */   updateRegex: () => (/* binding */ updateRegex)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"./src/world_info_optimizer/api.ts\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./store */ \"./src/world_info_optimizer/store.ts\");\n// src/world_info_optimizer/core.ts\n\n\n/**\n * 加载所有初始数据。\n * 这是应用启动时调用的主要数据获取函数。\n */\nconst loadAllData = async () => {\n    (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, isDataLoaded: false }));\n    try {\n        const context = _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getContext();\n        const { characters: allCharacters, characterId, chatId } = context;\n        const hasActiveCharacter = characterId !== undefined && characterId !== null;\n        const hasActiveChat = chatId !== undefined && chatId !== null;\n        // --- 并行获取基础数据 ---\n        const results = await Promise.allSettled([\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getRegexes(),\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookSettings(),\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebooks(),\n            hasActiveCharacter ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCharData() : Promise.resolve(null),\n            hasActiveCharacter ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCurrentCharLorebooks() : Promise.resolve(null),\n            hasActiveChat ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getChatLorebook() : Promise.resolve(null),\n        ]);\n        // --- 安全地从Promise结果中提取数据 ---\n        const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n        const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n        const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n        const charData = results[3].status === 'fulfilled' ? results[3].value : null;\n        const charLinkedBooks = results[4].status === 'fulfilled' ? results[4].value : null;\n        const chatLorebook = results[5].status === 'fulfilled' ? results[5].value : null;\n        // --- 数据处理 ---\n        // 1. 正则表达式处理\n        const globalRegexes = (allUIRegexes || []).filter(r => r.scope === 'global');\n        const characterRegexes = processCharacterRegexes(allUIRegexes, charData);\n        // 2. 世界书文件和启用状态\n        const enabledGlobalBooks = new Set(globalSettings?.selected_global_lorebooks || []);\n        const allLorebooks = (allBookFileNames || []).map(name => ({\n            name: name,\n            enabled: enabledGlobalBooks.has(name)\n        }));\n        // 3. 当前角色关联的世界书\n        const charBookSet = new Set();\n        if (charLinkedBooks?.primary)\n            charBookSet.add(charLinkedBooks.primary);\n        if (charLinkedBooks?.additional)\n            charLinkedBooks.additional.forEach(b => charBookSet.add(b));\n        const characterLorebooks = Array.from(charBookSet);\n        // 4. 计算所有角色对世界书的使用情况 (lorebookUsage)\n        const lorebookUsage = new Map();\n        const knownBookNames = new Set(allBookFileNames || []);\n        if (Array.isArray(allCharacters)) {\n            for (const char of allCharacters) {\n                if (!char?.name)\n                    continue;\n                // 注意: getCharLorebooks 是同步还是异步取决于TavernHelper的实现\n                // 为保险起见，我们假设它可能返回一个Promise\n                const books = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCharLorebooks({ name: char.name });\n                const charBooks = new Set();\n                if (books?.primary)\n                    charBooks.add(books.primary);\n                if (books?.additional)\n                    books.additional.forEach(b => charBooks.add(b));\n                charBooks.forEach(bookName => {\n                    if (!lorebookUsage.has(bookName)) {\n                        lorebookUsage.set(bookName, []);\n                    }\n                    lorebookUsage.get(bookName)?.push(char.name);\n                    knownBookNames.add(bookName); // 确保所有被使用的书都被加载\n                });\n            }\n        }\n        // 5. 汇总所有需要加载条目的世界书\n        characterLorebooks.forEach(b => knownBookNames.add(b));\n        if (chatLorebook) {\n            knownBookNames.add(chatLorebook);\n        }\n        // 6. 并行加载所有世界书的条目\n        const lorebookEntries = new Map();\n        const entryPromises = Array.from(knownBookNames).map(async (name) => {\n            const entries = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookEntries(name);\n            if (entries) {\n                lorebookEntries.set(name, entries);\n            }\n        });\n        await Promise.all(entryPromises);\n        // 7. 更新全局状态\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.setAllData)({\n            globalRegexes,\n            characterRegexes,\n            allLorebooks,\n            characterLorebooks,\n            chatLorebook: chatLorebook || null,\n            lorebookEntries,\n            lorebookUsage,\n        });\n    }\n    catch (error) {\n        console.error('[WIO Core] Failed to load all data:', error);\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, isDataLoaded: true })); // 即使失败也要结束加载状态\n    }\n};\n/**\n * 处理和合并来自UI和角色卡中的角色特定正则表达式。\n * @param allUIRegexes 从Tavern API获取的所有UI正则\n * @param charData 当前角色的数据\n * @returns 合并和去重后的角色正则表达式数组\n */\nfunction processCharacterRegexes(allUIRegexes, charData) {\n    const characterUIRegexes = allUIRegexes?.filter(r => r.scope === 'character') || [];\n    let cardRegexes = [];\n    if (charData && _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.Character) {\n        try {\n            const character = new _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.Character(charData);\n            cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                id: r.id || `card-${Date.now()}-${i}`,\n                script_name: r.scriptName || '未命名卡内正则',\n                find_regex: r.findRegex,\n                replace_string: r.replaceString,\n                enabled: !r.disabled,\n                scope: 'character',\n                source: 'card'\n            }));\n        }\n        catch (e) {\n            console.warn(\"[WIO Core] Couldn't parse character card regex scripts:\", e);\n        }\n    }\n    // 去重逻辑：UI中的正则优先\n    const uiRegexIdentifiers = new Set(characterUIRegexes.map(r => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n    const uniqueCardRegexes = cardRegexes.filter(r => {\n        const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n        return !uiRegexIdentifiers.has(identifier);\n    });\n    return [...characterUIRegexes, ...uniqueCardRegexes];\n}\n// --- Lorebook CRUD Operations ---\n/**\n * 创建一个新的空世界书。\n * @param bookName 新世界书的名称\n */\nconst createLorebook = async (bookName) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebook(bookName);\n    if (result !== null) {\n        await loadAllData(); // 重新加载所有数据以确保状态同步\n    }\n};\n/**\n * 重命名一个世界书。\n * @param oldName 旧名称\n * @param newName 新名称\n */\nconst renameLorebook = async (oldName, newName) => {\n    // 1. 获取旧书的所有条目\n    const entries = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookEntries(oldName);\n    if (entries === null) {\n        throw new Error(`Failed to get entries for lorebook: ${oldName}`);\n    }\n    // 2. 创建一个新书\n    const createResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebook(newName);\n    if (createResult === null) {\n        throw new Error(`Failed to create new lorebook: ${newName}`);\n    }\n    // 3. 将条目复制到新书\n    if (entries.length > 0) {\n        const setResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(newName, entries);\n        if (setResult === null) {\n            // 清理：如果条目设置失败，删除新建的书\n            await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(newName);\n            throw new Error(`Failed to set entries for new lorebook: ${newName}`);\n        }\n    }\n    // 4. 删除旧书\n    const deleteResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(oldName);\n    if (deleteResult === null) {\n        // 这不是一个关键错误，但应该记录下来\n        console.warn(`Failed to delete old lorebook \"${oldName}\" after renaming.`);\n    }\n    // 5. 刷新数据\n    await loadAllData();\n};\n/**\n * 删除一个世界书。\n * @param bookName 要删除的世界书的名称\n */\nconst deleteLorebook = async (bookName) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(bookName);\n    if (result !== null) {\n        await loadAllData();\n    }\n};\n// --- Lorebook Entry CRUD Operations ---\n/**\n * 在指定的世界书中创建一个新条目。\n * @param bookName 世界书名称\n * @param entryData 要创建的条目的数据\n */\nconst createLorebookEntry = async (bookName, entryData) => {\n    // Tavern API需要一个数组\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebookEntries(bookName, [entryData]);\n    if (result !== null && result.length > 0) {\n        const newState = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const newEntry = result[0]; // API会返回创建后的条目，包含UID\n        const currentEntries = newState.lorebookEntries.get(bookName) || [];\n        newState.lorebookEntries.set(bookName, [...currentEntries, newEntry]);\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(newState.lorebookEntries) }));\n    }\n};\n/**\n * 更新世界书中的一个现有条目。\n * @param bookName 世界书名称\n * @param uid 要更新的条目的唯一ID\n * @param updates 要应用于条目的部分更新\n */\nconst updateLorebookEntry = async (bookName, uid, updates) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const entries = state.lorebookEntries.get(bookName);\n    if (!entries) {\n        throw new Error(`[WIO Core] Book not found in state: ${bookName}`);\n    }\n    let entryUpdated = false;\n    const updatedEntries = entries.map(entry => {\n        if (entry.uid === uid) {\n            entryUpdated = true;\n            return { ...entry, ...updates };\n        }\n        return entry;\n    });\n    if (entryUpdated) {\n        const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(bookName, updatedEntries);\n        if (result !== null) {\n            state.lorebookEntries.set(bookName, updatedEntries);\n            (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));\n        }\n    }\n};\n/**\n * 从世界书中删除一个条目。\n * @param bookName 世界书名称\n * @param uid 要删除的条目的唯一ID\n */\nconst deleteLorebookEntry = async (bookName, uid) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebookEntries(bookName, [uid]);\n    if (result !== null) {\n        const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const entries = state.lorebookEntries.get(bookName) || [];\n        const filteredEntries = entries.filter(entry => entry.uid !== uid);\n        state.lorebookEntries.set(bookName, filteredEntries);\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));\n    }\n};\n// --- Global Settings ---\n/**\n * 设置一个全局世界书的启用状态。\n * @param bookName 世界书的名称\n * @param enabled true为启用, false为禁用\n */\nconst setGlobalLorebookEnabled = async (bookName, enabled) => {\n    const settings = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookSettings() || {};\n    if (!settings.selected_global_lorebooks) {\n        settings.selected_global_lorebooks = [];\n    }\n    const enabledBooks = new Set(settings.selected_global_lorebooks);\n    if (enabled) {\n        enabledBooks.add(bookName);\n    }\n    else {\n        enabledBooks.delete(bookName);\n    }\n    settings.selected_global_lorebooks = Array.from(enabledBooks);\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookSettings(settings);\n    if (result !== null) {\n        // 更新本地状态以立即反映UI变化\n        const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const book = state.allLorebooks.find(b => b.name === bookName);\n        if (book) {\n            book.enabled = enabled;\n            (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, allLorebooks: [...state.allLorebooks] }));\n        }\n    }\n};\n// --- Regex CRUD Operations ---\n/**\n * 提交正则表达式列表的更改。\n * @param updatedList 要提交的完整正则表达式列表 (仅限UI来源)\n */\nconst commitRegexChanges = async (updatedList) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.replaceRegexes(updatedList);\n    if (result !== null) {\n        // 成功后，重新加载所有数据以确保与后端完全同步\n        // 这是最简单可靠的方式，避免复杂的本地状态管理\n        await loadAllData();\n    }\n};\n/**\n * 创建一个新的正则表达式。\n * @param newRegexData 要创建的正则表达式的数据\n */\nconst createRegex = async (newRegexData) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    // 只处理UI来源的正则\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card')\n    ];\n    const finalNewRegex = {\n        id: `ui-${Date.now()}`,\n        enabled: true,\n        source: 'ui',\n        ...newRegexData,\n    };\n    await commitRegexChanges([...currentUIRegexes, finalNewRegex]);\n};\n/**\n * 更新一个现有的正则表达式。\n * @param regexId 要更新的正则表达式的ID\n * @param updates 要应用的更新\n */\nconst updateRegex = async (regexId, updates) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card')\n    ];\n    const updatedList = currentUIRegexes.map(r => r.id === regexId ? { ...r, ...updates } : r);\n    await commitRegexChanges(updatedList);\n};\n/**\n * 删除一个正则表达式。\n * @param regexId 要删除的正则表达式的ID\n */\nconst deleteRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card')\n    ];\n    const updatedList = currentUIRegexes.filter(r => r.id !== regexId);\n    await commitRegexChanges(updatedList);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/core.ts\n\n}");
  },
  "./src/world_info_optimizer/events.ts": 
  /*!********************************************!*\
  !*** ./src/world_info_optimizer/events.ts ***!
  \********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeEventHandlers: () => (/* binding */ initializeEventHandlers)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/world_info_optimizer/constants.ts\");\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core */ \"./src/world_info_optimizer/core.ts\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./store */ \"./src/world_info_optimizer/store.ts\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui */ \"./src/world_info_optimizer/ui.ts\");\n// src/world_info_optimizer/events.ts\n\n\n\n\nlet parentDoc;\nlet $;\n/**\n * 初始化所有UI事件处理器。\n * 这个函数应该在UI注入DOM后调用。\n * @param parentWindow 父窗口对象\n */\nconst initializeEventHandlers = (parentWindow) => {\n    parentDoc = parentWindow.document;\n    $ = parentWindow.jQuery;\n    const $body = $('body', parentDoc);\n    // --- 主面板和按钮的事件 ---\n    // 打开面板的按钮\n    $body.on('click', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}`, () => {\n        $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc).fadeIn(200);\n        // 首次打开时加载数据\n        if (!(0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)().isDataLoaded) {\n            (0,_core__WEBPACK_IMPORTED_MODULE_1__.loadAllData)();\n        }\n    });\n    // 关闭面板的按钮\n    $body.on('click', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} .wio-close-btn`, () => {\n        $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc).fadeOut(200);\n    });\n    // --- 事件委托：主面板内的所有点击事件 ---\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc);\n    $panel.on('click', async (event) => {\n        const $target = $(event.target);\n        // --- 标签页切换 ---\n        const tabButton = $target.closest('.wio-tab-btn');\n        if (tabButton.length) {\n            const tabId = tabButton.data('tab-id');\n            (0,_store__WEBPACK_IMPORTED_MODULE_2__.setActiveTab)(tabId);\n            return;\n        }\n        // --- 工具栏操作 ---\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.CREATE_LOREBOOK_BTN_ID}`).length) {\n            handleCreateBook();\n            return;\n        }\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}`).length) {\n            (0,_core__WEBPACK_IMPORTED_MODULE_1__.loadAllData)();\n            return;\n        }\n        // --- 世界书操作 ---\n        const renameBookBtn = $target.closest('.wio-rename-book-btn');\n        if (renameBookBtn.length) {\n            const bookName = renameBookBtn.closest('.wio-book-group').data('book-name');\n            handleRenameBook(bookName);\n            return;\n        }\n        const deleteBookBtn = $target.closest('.wio-delete-book-btn');\n        if (deleteBookBtn.length) {\n            const bookName = deleteBookBtn.closest('.wio-book-group').data('book-name');\n            handleDeleteBook(bookName);\n            return;\n        }\n        // --- 条目操作 ---\n        const createEntryBtn = $target.closest('.wio-create-entry-btn');\n        if (createEntryBtn.length) {\n            const bookName = createEntryBtn.data('book-name');\n            handleCreateEntry(bookName);\n            return;\n        }\n        const editEntryBtn = $target.closest('.wio-edit-entry-btn');\n        if (editEntryBtn.length) {\n            const entryItem = editEntryBtn.closest('.wio-entry-item');\n            const bookName = entryItem.data('book-name');\n            const uid = entryItem.data('uid');\n            handleEditEntry(bookName, uid);\n            return;\n        }\n        const deleteEntryBtn = $target.closest('.wio-delete-entry-btn');\n        if (deleteEntryBtn.length) {\n            const entryItem = deleteEntryBtn.closest('.wio-entry-item');\n            const bookName = entryItem.data('book-name');\n            const uid = entryItem.data('uid');\n            handleDeleteEntry(bookName, uid);\n            return;\n        }\n        // --- 正则操作 ---\n        const createRegexBtn = $target.closest('.wio-create-regex-btn');\n        if (createRegexBtn.length) {\n            const scope = createRegexBtn.data('scope');\n            handleCreateRegex(scope);\n            return;\n        }\n        const editRegexBtn = $target.closest('.wio-edit-regex-btn');\n        if (editRegexBtn.length) {\n            const regexItem = editRegexBtn.closest('.wio-regex-item');\n            const regexId = regexItem.data('id');\n            handleEditRegex(regexId);\n            return;\n        }\n        const deleteRegexBtn = $target.closest('.wio-delete-regex-btn');\n        if (deleteRegexBtn.length) {\n            const regexItem = deleteRegexBtn.closest('.wio-regex-item');\n            const regexId = regexItem.data('id');\n            handleDeleteRegex(regexId);\n            return;\n        }\n    });\n    // --- 事件委托：处理表单元素的 change 事件 ---\n    $panel.on('change', async (event) => {\n        const $target = $(event.target);\n        // --- 全局世界书启用/禁用切换 ---\n        if ($target.is('.wio-global-book-toggle')) {\n            const bookName = $target.closest('.wio-book-group').data('book-name');\n            const isEnabled = $target.prop('checked');\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.setGlobalLorebookEnabled)(bookName, isEnabled);\n            return;\n        }\n    });\n    // --- 事件委托：处理搜索框输入 ---\n    $panel.on('input', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}`, (event) => {\n        const query = $(event.target).val();\n        (0,_store__WEBPACK_IMPORTED_MODULE_2__.setSearchQuery)(query);\n    });\n};\n// --- 事件处理器具体实现 ---\n/**\n * 处理重命名世界书的逻辑。\n * @param bookName 要重命名的世界书的当前名称\n */\nconst handleRenameBook = async (bookName) => {\n    try {\n        const newName = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'prompt',\n            title: '重命名世界书',\n            text: `为 \"${bookName}\" 输入新的名称:`,\n            value: bookName,\n        });\n        if (typeof newName === 'string' && newName.trim() && newName !== bookName) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.renameLorebook)(bookName, newName.trim());\n        }\n    }\n    catch (error) {\n        // 用户取消了输入\n        console.log('Rename operation cancelled.');\n    }\n};\n/**\n * 处理删除世界书的逻辑。\n * @param bookName 要删除的世界书的名称\n */\nconst handleDeleteBook = async (bookName) => {\n    try {\n        const confirmation = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: `你确定要永久删除世界书 \"${bookName}\" 吗？此操作无法撤销。`,\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteLorebook)(bookName);\n        }\n    }\n    catch (error) {\n        // 用户取消了确认\n        console.log('Delete operation cancelled.');\n    }\n};\n/**\n * 处理创建新条目的逻辑。\n * @param bookName 新条目所属的世界书名称\n */\nconst handleCreateEntry = async (bookName) => {\n    try {\n        const newEntryData = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showEntryEditorModal)({\n            entry: { keys: [], content: '', comment: '' },\n            bookName,\n            isCreating: true,\n        });\n        // showEntryEditorModal 返回的 newEntryData 不包含 enabled 状态, 我们需要设置默认值\n        const entryToCreate = {\n            ...newEntryData,\n            enabled: true, // 新条目默认启用\n        };\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createLorebookEntry)(bookName, entryToCreate);\n    }\n    catch (error) {\n        console.log('Create entry operation cancelled.');\n    }\n};\n/**\n * 处理编辑现有条目的逻辑。\n * @param bookName 条目所属的世界书名称\n * @param uid 要编辑的条目的UID\n */\nconst handleEditEntry = async (bookName, uid) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const entry = state.lorebookEntries.get(bookName)?.find(e => e.uid === uid);\n    if (!entry) {\n        console.error(`Entry with UID ${uid} not found in book ${bookName}.`);\n        return;\n    }\n    try {\n        const updatedEntryData = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showEntryEditorModal)({\n            entry: { ...entry }, // 传入副本以防意外修改\n            bookName,\n            isCreating: false,\n        });\n        // 我们只需要更新发生变化的部分\n        const updates = {\n            comment: updatedEntryData.comment,\n            keys: updatedEntryData.keys,\n            content: updatedEntryData.content,\n        };\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateLorebookEntry)(bookName, uid, updates);\n    }\n    catch (error) {\n        console.log('Edit entry operation cancelled.');\n    }\n};\n/**\n * 处理删除条目的逻辑。\n * @param bookName 条目所属的世界书名称\n * @param uid 要删除的条目的UID\n */\nconst handleDeleteEntry = async (bookName, uid) => {\n    try {\n        const confirmation = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: `你确定要永久删除这个条目吗？`,\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteLorebookEntry)(bookName, uid);\n        }\n    }\n    catch (error) {\n        console.log('Delete entry operation cancelled.');\n    }\n};\n/**\n * 处理创建新世界书的逻辑。\n */\nconst handleCreateBook = async () => {\n    try {\n        const newName = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'prompt',\n            title: '创建新世界书',\n            text: '请输入新世界书的名称:',\n            value: 'New-Lorebook',\n        });\n        if (typeof newName === 'string' && newName.trim()) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createLorebook)(newName.trim());\n        }\n    }\n    catch (error) {\n        console.log('Create lorebook operation cancelled.');\n    }\n};\n// --- 正则表达式事件处理器 ---\nconst handleCreateRegex = async (scope) => {\n    try {\n        const newRegexData = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showRegexEditorModal)({\n            regex: { script_name: '新正则', find_regex: '', replace_string: '' },\n            isCreating: true,\n        });\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createRegex)({ ...newRegexData, scope });\n    }\n    catch (error) {\n        console.log('Create regex operation cancelled.');\n    }\n};\nconst handleEditRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const allRegexes = [...state.regexes.global, ...state.regexes.character];\n    const regex = allRegexes.find(r => r.id === regexId);\n    if (!regex) {\n        console.error(`Regex with ID ${regexId} not found.`);\n        return;\n    }\n    // 卡片内正则不可编辑\n    if (regex.source === 'card') {\n        await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showModal)({ type: 'alert', title: '操作无效', text: '无法编辑来自角色卡的正则表达式。' });\n        return;\n    }\n    try {\n        const updatedRegexData = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showRegexEditorModal)({\n            regex: { ...regex },\n            isCreating: false,\n        });\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateRegex)(regexId, updatedRegexData);\n    }\n    catch (error) {\n        console.log('Edit regex operation cancelled.');\n    }\n};\nconst handleDeleteRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const allRegexes = [...state.regexes.global, ...state.regexes.character];\n    const regex = allRegexes.find(r => r.id === regexId);\n    if (regex && regex.source === 'card') {\n        await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showModal)({ type: 'alert', title: '操作无效', text: '无法删除来自角色卡的正则表达式。' });\n        return;\n    }\n    try {\n        const confirmation = await (0,_ui__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: '你确定要永久删除这个正则表达式吗？',\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteRegex)(regexId);\n        }\n    }\n    catch (error) {\n        console.log('Delete regex operation cancelled.');\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/events.ts\n\n}");
  },
  "./src/world_info_optimizer/index.ts": 
  /*!*******************************************!*\
  !*** ./src/world_info_optimizer/index.ts ***!
  \*******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ui */ \"./src/world_info_optimizer/ui.ts\");\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./events */ \"./src/world_info_optimizer/events.ts\");\n/* harmony import */ var _ui_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/helpers */ \"./src/world_info_optimizer/ui/helpers.ts\");\n// src/world_info_optimizer/index.ts\n\n\n\n/**\n * 等待SillyTavern的核心DOM和API准备就绪。\n * @param callback 准备就绪后执行的回调函数\n */\nfunction onReady(callback) {\n    const domSelector = '#extensionsMenu';\n    const maxRetries = 100; // 等待最多约20秒\n    let retries = 0;\n    console.log('[WIO] Starting readiness check...');\n    const interval = setInterval(() => {\n        const parentWin = window.parent;\n        if (!parentWin) {\n            retries++;\n            return;\n        }\n        const domReady = parentWin.document.querySelector(domSelector) !== null;\n        const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n        if (domReady && apiReady) {\n            clearInterval(interval);\n            console.log('[WIO] SUCCESS: DOM and Core APIs are ready. Initializing script.');\n            try {\n                callback(parentWin);\n            }\n            catch (e) {\n                console.error('[WIO] FATAL: Error during main callback execution.', e);\n            }\n        }\n        else {\n            retries++;\n            if (retries > maxRetries) {\n                clearInterval(interval);\n                console.error('[WIO] FATAL: Readiness check timed out.');\n                if (!domReady)\n                    console.error(`[WIO] -> Failure: DOM element \"${domSelector}\" not found.`);\n                if (!apiReady)\n                    console.error(`[WIO] -> Failure: Core APIs not available. TavernHelper: ${!!parentWin.TavernHelper}, jQuery: ${!!parentWin.jQuery}`);\n            }\n        }\n    }, 200);\n}\n/**\n * 应用程序的主函数。\n * @param parentWindow 父窗口对象\n */\nfunction main(parentWindow) {\n    console.log('[WIO] Initializing World Info Optimizer...');\n    // 1. 注入UI元素到DOM中\n    (0,_ui__WEBPACK_IMPORTED_MODULE_0__.injectUI)(parentWindow);\n    // 2. 初始化UI模块，使其订阅状态变化\n    (0,_ui__WEBPACK_IMPORTED_MODULE_0__.initUI)();\n    // 3. 绑定所有事件处理器\n    (0,_events__WEBPACK_IMPORTED_MODULE_1__.initializeEventHandlers)(parentWindow);\n    // 4. 设置响应式设计支持\n    (0,_ui_helpers__WEBPACK_IMPORTED_MODULE_2__.setupResponsiveResizeHandler)();\n    (0,_ui_helpers__WEBPACK_IMPORTED_MODULE_2__.applyHighContrastMode)();\n    (0,_ui_helpers__WEBPACK_IMPORTED_MODULE_2__.applyReducedMotionMode)();\n    console.log('[WIO] World Info Optimizer initialized successfully.');\n}\n// --- 脚本启动 ---\n(() => {\n    // 使用IIFE封装，避免全局污染\n    console.log('[WIO Script] Execution started.');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/index.ts\n\n}");
  },
  "./src/world_info_optimizer/store.ts": 
  /*!*******************************************!*\
  !*** ./src/world_info_optimizer/store.ts ***!
  \*******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getState: () => (/* binding */ getState),\n/* harmony export */   resetState: () => (/* binding */ resetState),\n/* harmony export */   setActiveTab: () => (/* binding */ setActiveTab),\n/* harmony export */   setAllData: () => (/* binding */ setAllData),\n/* harmony export */   setDataLoaded: () => (/* binding */ setDataLoaded),\n/* harmony export */   setSearchQuery: () => (/* binding */ setSearchQuery),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   updateState: () => (/* binding */ updateState)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/world_info_optimizer/constants.ts\");\n// src/world_info_optimizer/store.ts\n\n// --- State Definition ---\nconst initialState = {\n    regexes: { global: [], character: [] },\n    lorebooks: { character: [] },\n    chatLorebook: null,\n    allLorebooks: [],\n    lorebookEntries: new Map(),\n    lorebookUsage: new Map(),\n    activeTab: _constants__WEBPACK_IMPORTED_MODULE_0__.INITIAL_ACTIVE_TAB,\n    isDataLoaded: false,\n    searchFilters: {\n        bookName: true,\n        entryName: true,\n        keywords: true,\n        content: true,\n    },\n    searchQuery: '',\n    multiSelectMode: false,\n    selectedItems: new Set(),\n};\n// 使用深拷贝来创建可变状态，避免直接修改initialState\nlet state = JSON.parse(JSON.stringify(initialState));\n// Map和Set不能通过JSON.stringify/parse正确克隆，需要手动重新创建\nstate.lorebookEntries = new Map();\nstate.lorebookUsage = new Map();\nstate.selectedItems = new Set();\nconst listeners = [];\n/**\n * 订阅状态变化。\n * @param listener 当状态更新时要调用的回调函数。\n * @returns 一个取消订阅的函数。\n */\nconst subscribe = (listener) => {\n    listeners.push(listener);\n    return () => {\n        const index = listeners.indexOf(listener);\n        if (index > -1) {\n            listeners.splice(index, 1);\n        }\n    };\n};\n/**\n * 通知所有监听器状态已更新。\n */\nconst notify = () => {\n    // 传递状态的深拷贝，以防监听器意外修改状态\n    const deepCopiedState = {\n        ...state,\n        lorebookEntries: new Map(state.lorebookEntries),\n        lorebookUsage: new Map(state.lorebookUsage),\n        selectedItems: new Set(state.selectedItems),\n    };\n    listeners.forEach(listener => listener(deepCopiedState));\n};\n// --- State Accessors and Mutators ---\n/**\n * 获取当前状态对象的快照。\n * @returns 当前应用状态的深拷贝。\n */\nconst getState = () => {\n    // 返回深拷贝以保证状态的不可变性\n    return {\n        ...state,\n        lorebookEntries: new Map(state.lorebookEntries),\n        lorebookUsage: new Map(state.lorebookUsage),\n        selectedItems: new Set(state.selectedItems),\n    };\n};\n/**\n * 更新应用状态并通知所有订阅者。\n * @param updater 一个函数，接收当前状态并返回一个新的（或修改过的）状态部分。\n */\nconst updateState = (updater) => {\n    const updates = updater(state);\n    state = { ...state, ...updates };\n    notify();\n};\n/**\n * 重置整个应用状态到初始值。\n */\nconst resetState = () => {\n    state = JSON.parse(JSON.stringify(initialState));\n    state.lorebookEntries = new Map();\n    state.lorebookUsage = new Map();\n    state.selectedItems = new Set();\n    notify();\n};\n// --- Specific State Updater Functions ---\nconst setActiveTab = (tabId) => {\n    updateState(s => ({ ...s, activeTab: tabId }));\n};\nconst setDataLoaded = (isLoaded) => {\n    updateState(s => ({ ...s, isDataLoaded: isLoaded }));\n};\nconst setSearchQuery = (query) => {\n    updateState(s => ({ ...s, searchQuery: query }));\n};\nconst setAllData = (data) => {\n    updateState(s => ({\n        ...s,\n        regexes: { global: data.globalRegexes, character: data.characterRegexes },\n        allLorebooks: data.allLorebooks,\n        lorebooks: { character: data.characterLorebooks },\n        chatLorebook: data.chatLorebook,\n        lorebookEntries: data.lorebookEntries,\n        lorebookUsage: data.lorebookUsage,\n        isDataLoaded: true,\n    }));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/store.ts\n\n}");
  },
  "./src/world_info_optimizer/ui.ts": 
  /*!****************************************!*\
  !*** ./src/world_info_optimizer/ui.ts ***!
  \****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initUI: () => (/* binding */ initUI),\n/* harmony export */   injectUI: () => (/* binding */ injectUI)\n/* harmony export */ });\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./store */ "./src/world_info_optimizer/store.ts");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ "./src/world_info_optimizer/constants.ts");\n/* harmony import */ var _ui_views__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/views */ "./src/world_info_optimizer/ui/views.ts");\n/* harmony import */ var _ui_modals__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/modals */ "./src/world_info_optimizer/ui/modals.ts");\n/* harmony import */ var _ui_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/helpers */ "./src/world_info_optimizer/ui/helpers.ts");\n// src/world_info_optimizer/ui.ts\n\n\n\n\n\n// --- Private Variables ---\nlet parentDoc;\nlet $;\n// --- Main Panel and Button Injection ---\nconst injectUI = (parentWindow) => {\n    parentDoc = parentWindow.document;\n    $ = parentWindow.jQuery;\n    if ($(`#${_constants__WEBPACK_IMPORTED_MODULE_1__.BUTTON_ID}`, parentDoc).length > 0) {\n        console.log(\'[WIO] UI already injected.\');\n        return;\n    }\n    // 使用CSS变量定义主题颜色和间距，便于统一管理和响应式调整\n    const styles = `\n        /* CSS变量定义 */\n        :root {\n            --wio-bg-primary: #333;\n            --wio-bg-secondary: #444;\n            --wio-bg-tertiary: #2a2a2a;\n            --wio-bg-toolbar: #3a3a3a;\n            --wio-text-primary: #eee;\n            --wio-text-secondary: #ccc;\n            --wio-border-color: #555;\n            --wio-highlight-color: #00aaff;\n            --wio-border-radius: 8px;\n            --wio-spacing-xs: 4px;\n            --wio-spacing-sm: 8px;\n            --wio-spacing-md: 10px;\n            --wio-spacing-lg: 15px;\n            --wio-font-size-sm: 12px;\n            --wio-font-size-md: 14px;\n            --wio-font-size-lg: 16px;\n            --wio-shadow: 0 5px 15px rgba(0,0,0,0.5);\n        }\n\n        /* 基础面板样式 */\n        #${_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID} {\n            display: none;\n            position: fixed;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            z-index: 10000;\n            overflow: hidden;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID} .wio-panel-inner {\n            display: flex;\n            flex-direction: column;\n            height: 100vh;\n            width: 100vw;\n            background-color: var(--wio-bg-primary);\n            color: var(--wio-text-primary);\n        }\n\n        /* 响应式面板调整 */\n        @media (min-width: 768px) {\n            #${_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID} {\n                top: 50%;\n                left: 50%;\n                right: auto;\n                bottom: auto;\n                transform: translate(-50%, -50%);\n                width: 80%;\n                max-width: 900px;\n                height: 70%;\n                border: 1px solid var(--wio-border-color);\n                border-radius: var(--wio-border-radius);\n                box-shadow: var(--wio-shadow);\n            }\n            \n            #${_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID} .wio-panel-inner {\n                height: 100%;\n                width: 100%;\n            }\n        }\n\n        /* 头部样式 */\n        .wio-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-secondary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        .wio-header h2 {\n            margin: 0;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-close-btn {\n            background: none;\n            border: none;\n            color: var(--wio-text-primary);\n            font-size: 24px;\n            cursor: pointer;\n            padding: var(--wio-spacing-xs);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }        \n        \n        /* 选项卡样式 */\n        .wio-tabs {\n            display: flex;\n            background-color: var(--wio-bg-tertiary);\n            overflow-x: auto;\n            white-space: nowrap;\n            flex-shrink: 0;\n            border-bottom: 1px solid var(--wio-border-color);\n            -ms-overflow-style: none;\n            scrollbar-width: none;\n        }\n        \n        .wio-tabs::-webkit-scrollbar {\n            display: none;\n        }\n        \n        .wio-tab-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: none;\n            background-color: transparent;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            border-bottom: 2px solid transparent;\n            flex-shrink: 0;\n            font-size: var(--wio-font-size-md);\n            transition: all 0.2s ease;\n            outline: none;\n        }\n        \n        .wio-tab-btn.active {\n            color: var(--wio-text-primary);\n            border-bottom-color: var(--wio-highlight-color);\n            background-color: rgba(0, 170, 255, 0.1);\n        }\n        \n        .wio-tab-btn:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n        \n        /* 工具栏样式 */\n        .wio-toolbar {\n            padding: var(--wio-spacing-md);\n            display: flex;\n            gap: var(--wio-spacing-md);\n            background-color: var(--wio-bg-toolbar);\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_1__.SEARCH_INPUT_ID} {\n            flex-grow: 1;\n            padding: var(--wio-spacing-md);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-primary);\n            color: var(--wio-text-primary);\n            font-size: var(--wio-font-size-md);\n            outline: none;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_1__.SEARCH_INPUT_ID}::placeholder {\n            color: var(--wio-text-secondary);\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_1__.SEARCH_INPUT_ID}:focus {\n            border-color: var(--wio-highlight-color);\n            box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.2);\n        }\n        \n        .wio-toolbar button {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-secondary);\n            color: var(--wio-text-primary);\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: var(--wio-spacing-xs);\n            transition: all 0.2s ease;\n            outline: none;\n            min-width: 44px;\n            min-height: 44px;\n        }\n        \n        .wio-toolbar button:hover {\n            background-color: var(--wio-highlight-color);\n            border-color: var(--wio-highlight-color);\n        }\n        \n        .wio-toolbar button:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 主内容区域样式 */\n        .wio-main-content {\n            flex-grow: 1;\n            overflow-y: auto;\n            padding: var(--wio-spacing-lg);\n        }\n        \n        /* 列表样式 */\n        .wio-book-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n        }\n        \n        .wio-book-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-secondary);\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-border-color);\n        }\n        \n        .wio-book-header h4 {\n            margin: 0;\n            flex-grow: 1;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-usage-pill {\n            padding: 2px 8px;\n            background-color: var(--wio-highlight-color);\n            color: white;\n            border-radius: 12px;\n            font-size: var(--wio-font-size-sm);\n        }\n        \n        .wio-item-controls {\n            display: flex;\n            gap: var(--wio-spacing-xs);\n        }\n        \n        .wio-item-controls button {\n            padding: var(--wio-spacing-xs) var(--wio-spacing-sm);\n            background-color: transparent;\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            color: var(--wio-text-primary);\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n        \n        .wio-item-controls button:hover {\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-entry-list {\n            background-color: var(--wio-bg-primary);\n        }\n        \n        .wio-entry-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-border-color);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-entry-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-entry-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n        }\n        \n        .wio-entry-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-entry-keys {\n            font-size: var(--wio-font-size-sm);\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            flex-grow: 1;\n            word-break: break-word;\n        }\n        \n        .wio-entry-actions,\n        .wio-regex-actions {\n            padding: var(--wio-spacing-md);\n            text-align: center;\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-create-entry-btn,\n        .wio-create-regex-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-highlight-color);\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: white;\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n        }\n\n        /* 正则表达式样式 */\n        .wio-regex-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n        }\n        \n        .wio-regex-group h3 {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            margin: 0;\n            background-color: var(--wio-bg-secondary);\n            border-bottom: 1px solid var(--wio-border-color);\n        }\n        \n        .wio-regex-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-border-color);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-regex-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-regex-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n            flex-wrap: wrap;\n        }\n        \n        .wio-regex-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-regex-find,\n        .wio-regex-replace {\n            background-color: var(--wio-bg-tertiary);\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-family: monospace;\n            font-size: var(--wio-font-size-sm);\n            word-break: break-all;\n        }\n        \n        .wio-info-text {\n            text-align: center;\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            padding: var(--wio-spacing-lg);\n        }\n        \n        .wio-search-highlight {\n            background-color: rgba(255, 255, 0, 0.3);\n            padding: 0 2px;\n            border-radius: 2px;\n        }\n\n        /* 页脚样式 */\n        .wio-footer {\n            padding: var(--wio-spacing-sm) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-tertiary);\n            font-size: var(--wio-font-size-sm);\n            text-align: right;\n            border-top: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n\n        /* 复选框样式优化 */\n        input[type="checkbox"] {\n            transform: scale(1.2);\n            accent-color: var(--wio-highlight-color);\n            margin-right: var(--wio-spacing-xs);\n        }\n        \n        /* 聚焦样式优化 */\n        :focus-visible {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 滚动条样式 */\n        .wio-main-content::-webkit-scrollbar,\n        .wio-tabs::-webkit-scrollbar {\n            width: 8px;\n            height: 8px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-track,\n        .wio-tabs::-webkit-scrollbar-track {\n            background: var(--wio-bg-tertiary);\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb,\n        .wio-tabs::-webkit-scrollbar-thumb {\n            background: var(--wio-border-color);\n            border-radius: 4px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb:hover,\n        .wio-tabs::-webkit-scrollbar-thumb:hover {\n            background: #777;\n        }\n\n        /* 媒体查询：小型设备优化 */\n        @media (max-width: 767px) {\n            :root {\n                --wio-spacing-xs: 2px;\n                --wio-spacing-sm: 6px;\n                --wio-spacing-md: 8px;\n                --wio-spacing-lg: 10px;\n                --wio-font-size-sm: 11px;\n                --wio-font-size-md: 13px;\n                --wio-font-size-lg: 15px;\n            }\n            \n            .wio-header h2 {\n                font-size: var(--wio-font-size-md);\n            }\n            \n            .wio-entry-main {\n                flex-wrap: wrap;\n            }\n            \n            .wio-entry-name {\n                flex-basis: 100%;\n                margin-bottom: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-main {\n                flex-direction: column;\n                align-items: flex-start;\n                gap: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-name,\n            .wio-regex-find,\n            .wio-regex-replace {\n                width: 100%;\n                box-sizing: border-box;\n            }\n            \n            .wio-toolbar {\n                flex-wrap: wrap;\n            }\n            \n            #${_constants__WEBPACK_IMPORTED_MODULE_1__.SEARCH_INPUT_ID} {\n                width: 100%;\n            }\n            \n            /* 触摸目标优化 */\n            button,\n            input[type="checkbox"] {\n                touch-action: manipulation;\n            }\n        }\n        \n        /* 平板设备优化 */\n        @media (min-width: 768px) and (max-width: 1024px) {\n            #${_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID} {\n                width: 90%;\n                height: 80%;\n            }\n        }\n\n        /* 高对比度模式支持 */\n        @media (prefers-contrast: high) {\n            :root {\n                --wio-border-color: #fff;\n                --wio-bg-primary: #000;\n                --wio-bg-secondary: #333;\n                --wio-bg-tertiary: #222;\n                --wio-bg-toolbar: #444;\n                --wio-text-primary: #fff;\n                --wio-text-secondary: #ddd;\n                --wio-highlight-color: #ff0;\n            }\n        }\n\n        /* 减少动画模式支持 */\n        @media (prefers-reduced-motion: reduce) {\n            * {\n                animation-duration: 0.01ms !important;\n                animation-iteration-count: 1 !important;\n                transition-duration: 0.01ms !important;\n            }\n        }\n    `;\n    const styleSheet = parentDoc.createElement("style");\n    styleSheet.type = "text/css";\n    styleSheet.innerText = styles;\n    parentDoc.head.appendChild(styleSheet);\n    const panelHtml = `\n        <div id="${_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID}">\n            <div class="wio-panel-inner">\n                <div class="wio-header">\n                    <h2>世界书 & 正则便捷管理 (WIO)</h2>\n                    <button class="wio-close-btn" aria-label="关闭面板">&times;</button>\n                </div>\n                <div class="wio-tabs" role="tablist">\n                    <button class="wio-tab-btn" data-tab-id="global-lore" role="tab" aria-controls="global-lore-content" aria-selected="false">全局世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="char-lore" role="tab" aria-controls="char-lore-content" aria-selected="false">角色世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="chat-lore" role="tab" aria-controls="chat-lore-content" aria-selected="false">聊天世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="global-regex" role="tab" aria-controls="global-regex-content" aria-selected="false">全局正则</button>\n                    <button class="wio-tab-btn" data-tab-id="char-regex" role="tab" aria-controls="char-regex-content" aria-selected="false">角色正则</button>\n                </div>\n                <div class="wio-toolbar">\n                    <input type="search" id="${_constants__WEBPACK_IMPORTED_MODULE_1__.SEARCH_INPUT_ID}" placeholder="搜索..." aria-label="搜索内容">\n                    <button id="${_constants__WEBPACK_IMPORTED_MODULE_1__.REFRESH_BTN_ID}" title="刷新数据" aria-label="刷新数据"><i class="fa-solid fa-sync"></i></button>\n                    <button id="${_constants__WEBPACK_IMPORTED_MODULE_1__.CREATE_LOREBOOK_BTN_ID}" title="新建世界书" aria-label="新建世界书"><i class="fa-solid fa-plus"></i></button>\n                </div>\n                <div class="wio-main-content" role="main" id="tab-content-container"></div>\n                <div class="wio-footer">\n                    <span>WIO v3.0 (Refactored)</span>\n                </div>\n            </div>\n        </div>\n    `;\n    $(\'body\', parentDoc).append(panelHtml);\n    const extensionButton = `\n        <div id="${_constants__WEBPACK_IMPORTED_MODULE_1__.BUTTON_ID}" class="list-group-item">\n            <img src="${_constants__WEBPACK_IMPORTED_MODULE_1__.BUTTON_ICON_URL}" style="width: 24px; height: 24px; margin-right: 10px;">\n            <span>${_constants__WEBPACK_IMPORTED_MODULE_1__.BUTTON_TEXT_IN_MENU}</span>\n        </div>\n    `;\n    $(\'#extensions_list\', parentDoc).append(extensionButton);\n    // 添加键盘导航支持\n    addKeyboardNavigation();\n    // 添加响应式布局监听器\n    (0,_ui_helpers__WEBPACK_IMPORTED_MODULE_4__.addResponsiveListener)(parentWindow, () => {\n        updatePanelLayout();\n    });\n    console.log(\'[WIO] UI Injected successfully.\');\n};\n// --- Utility Functions ---\n// 添加键盘导航支持\nconst addKeyboardNavigation = () => {\n    if (!parentDoc)\n        return;\n    const panel = parentDoc.getElementById(_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID);\n    const closeBtn = panel?.querySelector(\'.wio-close-btn\');\n    const searchInput = panel?.querySelector(`#${_constants__WEBPACK_IMPORTED_MODULE_1__.SEARCH_INPUT_ID}`);\n    const tabButtons = panel?.querySelectorAll(\'.wio-tab-btn\');\n    if (!panel || !closeBtn || !searchInput || !tabButtons)\n        return;\n    // 监听全局键盘事件\n    panel.addEventListener(\'keydown\', (e) => {\n        // ESC键关闭面板（当没有模态框打开时）\n        if (e.key === \'Escape\') {\n            if (!parentDoc.querySelector(\'.swal2-container\')) {\n                closeBtn.click();\n            }\n        }\n        // F1键显示帮助\n        if (e.key === \'F1\') {\n            e.preventDefault();\n            (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n                type: \'alert\',\n                title: \'帮助\',\n                text: \'世界书优化器快捷键:\\n- Tab: 导航到下一个元素\\n- Shift+Tab: 导航到上一个元素\\n- Enter: 确认操作\\n- Escape: 关闭面板或取消操作\\n- Ctrl+F: 聚焦搜索框\\n- F1: 显示此帮助信息\'\n            });\n        }\n        // Ctrl+F 聚焦搜索框\n        if (e.ctrlKey && e.key === \'f\') {\n            e.preventDefault();\n            searchInput.focus();\n            searchInput.select();\n        }\n        // Ctrl+Shift+Tab / Ctrl+Tab 在标签页之间导航\n        if (e.ctrlKey && (e.key === \'Tab\' || e.key === \'Shift\')) {\n            if (e.key === \'Tab\') {\n                e.preventDefault();\n                const activeIndex = Array.from(tabButtons).findIndex(btn => btn.classList.contains(\'active\'));\n                const nextIndex = e.shiftKey\n                    ? (activeIndex - 1 + tabButtons.length) % tabButtons.length\n                    : (activeIndex + 1) % tabButtons.length;\n                tabButtons[nextIndex].click();\n                tabButtons[nextIndex].focus();\n            }\n        }\n    });\n};\n// 更新面板布局\nconst updatePanelLayout = () => {\n    if (!parentDoc)\n        return;\n    const panel = parentDoc.getElementById(_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID);\n    if (!panel)\n        return;\n    const screenSize = (0,_ui_helpers__WEBPACK_IMPORTED_MODULE_4__.getScreenSize)();\n    // 根据屏幕尺寸调整UI元素\n    if (screenSize === \'small\') {\n        // 移动端特有调整\n    }\n    else if (screenSize === \'medium\') {\n        // 平板端特有调整\n    }\n    else {\n        // 桌面端特有调整\n    }\n};\n// --- Core Render Logic ---\nconst render = () => {\n    if (!parentDoc)\n        return;\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_0__.getState)();\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_1__.PANEL_ID}`, parentDoc);\n    if (!$panel.length)\n        return;\n    $panel.find(\'.wio-tab-btn\').removeClass(\'active\');\n    $panel.find(`.wio-tab-btn[data-tab-id="${state.activeTab}"]`).addClass(\'active\');\n    const $mainContent = $panel.find(\'.wio-main-content\');\n    if (!state.isDataLoaded) {\n        $mainContent.html(\'<p class="wio-info-text">正在加载数据...</p>\');\n        return;\n    }\n    const searchTerm = state.searchQuery.toLowerCase();\n    const $searchInput = $(`#${_constants__WEBPACK_IMPORTED_MODULE_1__.SEARCH_INPUT_ID}`, parentDoc);\n    if ($searchInput.val() !== state.searchQuery) {\n        $searchInput.val(state.searchQuery);\n    }\n    switch (state.activeTab) {\n        case \'global-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderGlobalLorebookView)(state, searchTerm));\n            break;\n        case \'char-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderCharacterLorebookView)(state, searchTerm));\n            break;\n        case \'chat-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderChatLorebookView)(state, searchTerm));\n            break;\n        case \'global-regex\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderRegexView)(state.regexes.global, searchTerm, \'全局正则\', \'global\'));\n            break;\n        case \'char-regex\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderRegexView)(state.regexes.character, searchTerm, \'角色正则\', \'character\'));\n            break;\n        default:\n            $mainContent.html(`<p>未知视图: ${state.activeTab}</p>`);\n    }\n};\n// --- UI Initialization ---\nconst initUI = () => {\n    (0,_store__WEBPACK_IMPORTED_MODULE_0__.subscribe)(render);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui.ts\n\n}');
  },
  "./src/world_info_optimizer/ui/elements.ts": 
  /*!*************************************************!*\
  !*** ./src/world_info_optimizer/ui/elements.ts ***!
  \*************************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEntryElement: () => (/* binding */ createEntryElement),\n/* harmony export */   createLorebookElement: () => (/* binding */ createLorebookElement),\n/* harmony export */   createRegexItemElement: () => (/* binding */ createRegexItemElement)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ "./src/world_info_optimizer/ui/helpers.ts");\n// src/world_info_optimizer/ui/elements.ts\n\nconst createLorebookElement = (book, state, searchTerm, isGlobal = true, entriesToShow) => {\n    const entries = entriesToShow !== undefined ? entriesToShow : (state.lorebookEntries.get(book.name) || []);\n    const totalEntries = state.lorebookEntries.get(book.name)?.length || 0;\n    const usage = state.lorebookUsage.get(book.name) || [];\n    const entryCountText = searchTerm && entries.length !== totalEntries\n        ? `${entries.length} / ${totalEntries}`\n        : `${totalEntries}`;\n    return `\n        <div class="wio-book-group" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}">\n            <div class="wio-book-header">\n                ${isGlobal ? `<input type="checkbox" ${book.enabled ? \'checked\' : \'\'} class="wio-global-book-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}">` : \'\'}\n                <h4>${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(book.name, searchTerm)} <span class="wio-entry-count">(${entryCountText})</span></h4>\n                ${usage.length > 0 ? `<span class="wio-usage-pill" title="被 ${usage.join(\', \')} 使用" aria-label="被 ${usage.length} 个角色使用">${usage.length}</span>` : \'\'}\n                <div class="wio-item-controls">\n                    <button class="wio-rename-book-btn" title="重命名" aria-label="重命名 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}"><i class="fa-solid fa-pencil"></i></button>\n                    <button class="wio-delete-book-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}"><i class="fa-solid fa-trash-can"></i></button>\n                </div>\n            </div>\n            <div class="wio-entry-list">\n                ${entries.map(entry => createEntryElement(entry, book.name, searchTerm)).join(\'\')}\n                <div class="wio-entry-actions"><button class="wio-create-entry-btn" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}" aria-label="在 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)} 中新建条目">+ 新建条目</button></div>\n            </div>\n        </div>\n    `;\n};\nconst createEntryElement = (entry, bookName, searchTerm) => {\n    const displayName = entry.comment || \'未命名条目\';\n    return `\n        <div class="wio-entry-item" data-uid="${entry.uid}" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(bookName)}">\n            <div class="wio-entry-main">\n                <input type="checkbox" ${entry.enabled ? \'checked\' : \'\'} class="wio-entry-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}">\n                <span class="wio-entry-name">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(displayName, searchTerm)}</span>\n                <span class="wio-entry-keys">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)((entry.keys || []).join(\', \'), searchTerm)}</span>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-entry-btn" title="编辑" aria-label="编辑 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-entry-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `;\n};\nconst createRegexItemElement = (regex, searchTerm) => {\n    const regexId = regex.id || `${regex.scope}-${btoa(unescape(encodeURIComponent(regex.script_name + regex.find_regex)))}`;\n    const displayName = regex.script_name || \'未命名正则\';\n    return `\n        <div class="wio-regex-item" data-id="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regexId)}" data-scope="${regex.scope}">\n            <div class="wio-regex-main">\n                <input type="checkbox" ${regex.enabled ? \'checked\' : \'\'} class="wio-regex-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}">\n                <span class="wio-regex-name">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(displayName, searchTerm)} ${regex.source === \'card\' ? \'<span class="wio-regex-source-badge" aria-label="卡内正则">(卡)</span>\' : \'\'}</span>\n                <code class="wio-regex-find" title="查找: ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.find_regex)}">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(regex.find_regex, searchTerm)}</code>\n                <i class="fa-solid fa-arrow-right-long" aria-hidden="true"></i>\n                <code class="wio-regex-replace" title="替换为: ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.replace_string)}">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(regex.replace_string, searchTerm)}</code>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-regex-btn" title="编辑" aria-label="编辑 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-regex-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/elements.ts\n\n}');
  },
  "./src/world_info_optimizer/ui/helpers.ts": 
  /*!************************************************!*\
  !*** ./src/world_info_optimizer/ui/helpers.ts ***!
  \************************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addResponsiveListener: () => (/* binding */ addResponsiveListener),\n/* harmony export */   applyHighContrastMode: () => (/* binding */ applyHighContrastMode),\n/* harmony export */   applyReducedMotionMode: () => (/* binding */ applyReducedMotionMode),\n/* harmony export */   escapeHtml: () => (/* binding */ escapeHtml),\n/* harmony export */   getResponsiveClass: () => (/* binding */ getResponsiveClass),\n/* harmony export */   getScreenSize: () => (/* binding */ getScreenSize),\n/* harmony export */   highlightText: () => (/* binding */ highlightText),\n/* harmony export */   isMobileView: () => (/* binding */ isMobileView),\n/* harmony export */   removeResponsiveListener: () => (/* binding */ removeResponsiveListener),\n/* harmony export */   setupResponsiveResizeHandler: () => (/* binding */ setupResponsiveResizeHandler)\n/* harmony export */ });\n// src/world_info_optimizer/ui/helpers.ts\nconst escapeHtml = (text) => {\n    if (typeof text !== 'string')\n        text = String(text);\n    const p = document.createElement('p');\n    p.textContent = text;\n    return p.innerHTML;\n};\nconst highlightText = (text, searchTerm) => {\n    if (!searchTerm || !text)\n        return escapeHtml(text);\n    const escapedText = escapeHtml(text);\n    const htmlSafeSearchTerm = escapeHtml(searchTerm);\n    const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n    const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n    return escapedText.replace(regex, '<mark class=\"wio-search-highlight\">$1</mark>');\n};\n/**\n * 获取响应式CSS类名\n * @param componentType 组件类型\n * @returns 响应式CSS类名字符串\n */\nconst getResponsiveClass = (componentType) => {\n    const responsiveClasses = {\n        'modal': 'wio-modal-content-responsive',\n        'panel': 'wio-panel-responsive',\n        'button': 'wio-button-responsive',\n        'form-group': 'wio-form-group-responsive',\n        'input': 'wio-input-responsive',\n        'list': 'wio-list-responsive'\n    };\n    return responsiveClasses[componentType] || '';\n};\n/**\n * 检查当前屏幕尺寸\n * @returns 当前屏幕尺寸类型\n */\nconst getScreenSize = () => {\n    const width = window.innerWidth;\n    if (width < 768)\n        return 'small';\n    if (width < 1200)\n        return 'medium';\n    return 'large';\n};\n/**\n * 判断是否需要显示移动端视图\n * @returns 是否为移动视图\n */\nconst isMobileView = () => {\n    return getScreenSize() === 'small';\n};\n/**\n * 为元素添加响应式调整事件\n * @param element 目标元素\n * @param callback 响应式调整回调函数\n */\nconst addResponsiveListener = (element, callback) => {\n    const handleResize = () => {\n        const screenSize = getScreenSize();\n        callback(screenSize);\n    };\n    window.addEventListener('resize', handleResize);\n    // 立即执行一次回调以应用初始状态\n    handleResize();\n    // 添加清理函数到元素上，便于后续移除事件监听器\n    element._responsiveCleanup = () => {\n        window.removeEventListener('resize', handleResize);\n    };\n};\n/**\n * 移除响应式调整事件\n * @param element 目标元素\n */\nconst removeResponsiveListener = (element) => {\n    if (element._responsiveCleanup) {\n        element._responsiveCleanup();\n        delete element._responsiveCleanup;\n    }\n};\n/**\n * 设置全局响应式尺寸调整处理器\n */\nconst setupResponsiveResizeHandler = () => {\n    let currentSize = getScreenSize();\n    const handleResize = () => {\n        const newSize = getScreenSize();\n        if (newSize !== currentSize) {\n            currentSize = newSize;\n            // 触发自定义响应式调整事件\n            const event = new CustomEvent('wio-responsive-resize', {\n                detail: { screenSize: newSize }\n            });\n            document.dispatchEvent(event);\n            console.log(`[WIO] Screen size changed to: ${newSize}`);\n        }\n    };\n    window.addEventListener('resize', handleResize);\n    console.log('[WIO] Responsive resize handler initialized.');\n};\n/**\n * 应用高对比度模式\n */\nconst applyHighContrastMode = () => {\n    // 检查用户是否有高对比度偏好\n    const mediaQuery = window.matchMedia('(prefers-contrast: high)');\n    const updateContrastMode = (e) => {\n        if (e.matches) {\n            document.documentElement.classList.add('wio-high-contrast');\n            console.log('[WIO] High contrast mode enabled.');\n        }\n        else {\n            document.documentElement.classList.remove('wio-high-contrast');\n            console.log('[WIO] High contrast mode disabled.');\n        }\n    };\n    // 初始应用\n    if (mediaQuery.matches) {\n        document.documentElement.classList.add('wio-high-contrast');\n    }\n    // 监听变化\n    mediaQuery.addEventListener('change', updateContrastMode);\n    console.log('[WIO] High contrast mode support initialized.');\n};\n/**\n * 应用减少动画模式\n */\nconst applyReducedMotionMode = () => {\n    // 检查用户是否有减少动画偏好\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');\n    const updateMotionMode = (e) => {\n        if (e.matches) {\n            document.documentElement.classList.add('wio-reduced-motion');\n            console.log('[WIO] Reduced motion mode enabled.');\n        }\n        else {\n            document.documentElement.classList.remove('wio-reduced-motion');\n            console.log('[WIO] Reduced motion mode disabled.');\n        }\n    };\n    // 初始应用\n    if (mediaQuery.matches) {\n        document.documentElement.classList.add('wio-reduced-motion');\n    }\n    // 监听变化\n    mediaQuery.addEventListener('change', updateMotionMode);\n    console.log('[WIO] Reduced motion mode support initialized.');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/helpers.ts\n\n}");
  },
  "./src/world_info_optimizer/ui/modals.ts": 
  /*!***********************************************!*\
  !*** ./src/world_info_optimizer/ui/modals.ts ***!
  \***********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   showEntryEditorModal: () => (/* binding */ showEntryEditorModal),\n/* harmony export */   showModal: () => (/* binding */ showModal),\n/* harmony export */   showProgressToast: () => (/* binding */ showProgressToast),\n/* harmony export */   showRegexEditorModal: () => (/* binding */ showRegexEditorModal),\n/* harmony export */   showSuccessTick: () => (/* binding */ showSuccessTick)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"./src/world_info_optimizer/ui/helpers.ts\");\n// src/world_info_optimizer/ui/modals.ts\n\nconst showModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return new Promise((resolve, reject) => {\n            const result = window.parent.prompt(options.text, typeof options.value === 'string' ? options.value : '');\n            if (result !== null)\n                resolve(result);\n            else\n                reject(new Error('Prompt cancelled'));\n        });\n    }\n    const type = options.type || 'alert';\n    switch (type) {\n        case 'confirm':\n            return Swal.fire({\n                title: options.title || '确认',\n                text: options.text,\n                icon: 'warning',\n                showCancelButton: true,\n                confirmButtonText: '确认',\n                cancelButtonText: '取消',\n            }).then((result) => {\n                if (result.isConfirmed) {\n                    return true;\n                }\n                throw new Error('Confirmation cancelled');\n            });\n        case 'prompt':\n            return Swal.fire({\n                title: options.title,\n                text: options.text,\n                input: 'text',\n                inputValue: options.value || '',\n                showCancelButton: true,\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n            }).then((result) => {\n                if (result.isConfirmed && typeof result.value === 'string') {\n                    return result.value;\n                }\n                throw new Error('Prompt cancelled');\n            });\n        case 'alert':\n        default:\n            return Swal.fire({\n                title: options.title || '提示',\n                text: options.text,\n                icon: 'info',\n            }).then(() => true);\n    }\n};\nconst showSuccessTick = (message = '操作成功', duration = 1500) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        console.log(`[SUCCESS] ${message}`);\n        return;\n    }\n    Swal.fire({\n        toast: true,\n        position: 'top-end',\n        icon: 'success',\n        title: message,\n        showConfirmButton: false,\n        timer: duration,\n        timerProgressBar: true,\n    });\n};\nconst showEntryEditorModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return Promise.reject(new Error('Swal not found!'));\n    }\n    const { entry, isCreating, bookName } = options;\n    const title = isCreating ? `在 \"${bookName}\" 中创建新条目` : `编辑条目: ${entry.comment}`;\n    return Swal.fire({\n        title: title,\n        html: `\n            <div style=\"text-align: left;\">\n                <label for=\"swal-comment\" class=\"swal2-label\">注释 (条目名)</label>\n                <input id=\"swal-comment\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(entry.comment || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-keys\" class=\"swal2-label\">关键词 (逗号分隔)</label>\n                <input id=\"swal-keys\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)((entry.keys || []).join(', '))}\">\n\n                <label for=\"swal-content\" class=\"swal2-label\">内容</label>\n                <textarea id=\"swal-content\" class=\"swal2-textarea\">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(entry.content || '')}</textarea>\n            </div>\n        `,\n        showCancelButton: true,\n        confirmButtonText: '保存',\n        cancelButtonText: '取消',\n        preConfirm: () => {\n            const comment = document.getElementById('swal-comment').value;\n            const keys = document.getElementById('swal-keys').value;\n            const content = document.getElementById('swal-content').value;\n            if (!comment && !keys) {\n                Swal.showValidationMessage('注释和关键词不能都为空');\n                return false;\n            }\n            return { comment, keys, content };\n        }\n    }).then((result) => {\n        if (result.isConfirmed) {\n            const { comment, keys, content } = result.value;\n            return {\n                ...entry,\n                comment,\n                keys: keys.split(',').map((k) => k.trim()).filter(Boolean),\n                content,\n            };\n        }\n        throw new Error('Entry editor cancelled');\n    });\n};\nconst showProgressToast = (initialMessage = '正在处理...') => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        console.log(`[PROGRESS] ${initialMessage}`);\n        return {\n            update: (newMessage) => console.log(`[PROGRESS UPDATE] ${newMessage}`),\n            remove: () => console.log(`[PROGRESS] Done.`),\n        };\n    }\n    Swal.fire({\n        toast: true,\n        position: 'top-end',\n        title: initialMessage,\n        showConfirmButton: false,\n        didOpen: () => {\n            Swal.showLoading();\n        }\n    });\n    return {\n        update: (newMessage) => {\n            const titleElement = Swal.getTitle();\n            if (titleElement) {\n                titleElement.textContent = newMessage;\n            }\n        },\n        remove: () => Swal.close(),\n    };\n};\nconst showRegexEditorModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return Promise.reject(new Error('Swal not found!'));\n    }\n    const { regex, isCreating } = options;\n    const title = isCreating ? '创建新正则' : `编辑正则: ${regex.script_name}`;\n    return Swal.fire({\n        title: title,\n        html: `\n            <div style=\"text-align: left;\">\n                <label for=\"swal-script-name\" class=\"swal2-label\">名称</label>\n                <input id=\"swal-script-name\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.script_name || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-find-regex\" class=\"swal2-label\">查找 (正则表达式)</label>\n                <input id=\"swal-find-regex\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.find_regex || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-replace-string\" class=\"swal2-label\">替换为</label>\n                <input id=\"swal-replace-string\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.replace_string || '')}\">\n            </div>\n        `,\n        showCancelButton: true,\n        confirmButtonText: '保存',\n        cancelButtonText: '取消',\n        preConfirm: () => {\n            const script_name = document.getElementById('swal-script-name').value;\n            const find_regex = document.getElementById('swal-find-regex').value;\n            const replace_string = document.getElementById('swal-replace-string').value;\n            if (!script_name || !find_regex) {\n                Swal.showValidationMessage('名称和查找正则不能为空');\n                return false;\n            }\n            return { script_name, find_regex, replace_string };\n        }\n    }).then((result) => {\n        if (result.isConfirmed) {\n            const { script_name, find_regex, replace_string } = result.value;\n            return {\n                ...regex,\n                script_name,\n                find_regex,\n                replace_string,\n            };\n        }\n        throw new Error('Regex editor cancelled');\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/modals.ts\n\n}");
  },
  "./src/world_info_optimizer/ui/views.ts": 
  /*!**********************************************!*\
  !*** ./src/world_info_optimizer/ui/views.ts ***!
  \**********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderCharacterLorebookView: () => (/* binding */ renderCharacterLorebookView),\n/* harmony export */   renderChatLorebookView: () => (/* binding */ renderChatLorebookView),\n/* harmony export */   renderGlobalLorebookView: () => (/* binding */ renderGlobalLorebookView),\n/* harmony export */   renderRegexView: () => (/* binding */ renderRegexView)\n/* harmony export */ });\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../store */ \"./src/world_info_optimizer/store.ts\");\n/* harmony import */ var _elements__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./elements */ \"./src/world_info_optimizer/ui/elements.ts\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers */ \"./src/world_info_optimizer/ui/helpers.ts\");\n// src/world_info_optimizer/ui/views.ts\n\n\n\nconst renderGlobalLorebookView = (state, searchTerm) => {\n    const books = [...state.allLorebooks].sort((a, b) => (b.enabled ? 1 : -1) - (a.enabled ? 1 : -1) || a.name.localeCompare(b.name));\n    if (books.length === 0)\n        return `<p class=\"wio-info-text\">没有找到全局世界书。</p>`;\n    if (!searchTerm) {\n        return `<div class=\"wio-content-container\">${books.map(book => (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(book, state, searchTerm)).join('')}</div>`;\n    }\n    const filteredBookHtml = books.map(book => {\n        const bookNameMatches = book.name.toLowerCase().includes(searchTerm);\n        const entries = state.lorebookEntries.get(book.name) || [];\n        const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n            (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n            (entry.content || '').toLowerCase().includes(searchTerm));\n        if (bookNameMatches || filteredEntries.length > 0) {\n            const entriesToShow = bookNameMatches ? entries : filteredEntries;\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(book, state, searchTerm, true, entriesToShow);\n        }\n        return '';\n    }).join('');\n    return filteredBookHtml ? `<div class=\"wio-content-container\">${filteredBookHtml}</div>` : `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderCharacterLorebookView = (state, searchTerm) => {\n    const linkedBooks = state.lorebooks.character;\n    if (linkedBooks.length === 0)\n        return `<p class=\"wio-info-text\">当前角色没有绑定的世界书。</p>`;\n    if (!searchTerm) {\n        return `<div class=\"wio-content-container\">${linkedBooks.map(bookName => {\n            const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false);\n        }).join('')}</div>`;\n    }\n    const filteredBookHtml = linkedBooks.map(bookName => {\n        const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n        const bookNameMatches = bookFile.name.toLowerCase().includes(searchTerm);\n        const entries = state.lorebookEntries.get(bookFile.name) || [];\n        const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n            (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n            (entry.content || '').toLowerCase().includes(searchTerm));\n        if (bookNameMatches || filteredEntries.length > 0) {\n            const entriesToShow = bookNameMatches ? entries : filteredEntries;\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false, entriesToShow);\n        }\n        return '';\n    }).join('');\n    return filteredBookHtml ? `<div class=\"wio-content-container\">${filteredBookHtml}</div>` : `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderChatLorebookView = (state, searchTerm) => {\n    const bookName = state.chatLorebook;\n    if (!bookName)\n        return `<p class=\"wio-info-text\">当前聊天没有绑定的世界书。</p>`;\n    const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n    if (!searchTerm) {\n        return `<div class=\"wio-content-container\">${(0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false)}</div>`;\n    }\n    const bookNameMatches = bookFile.name.toLowerCase().includes(searchTerm);\n    const entries = state.lorebookEntries.get(bookFile.name) || [];\n    const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n        (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n        (entry.content || '').toLowerCase().includes(searchTerm));\n    if (bookNameMatches || filteredEntries.length > 0) {\n        const entriesToShow = bookNameMatches ? entries : filteredEntries;\n        return `<div class=\"wio-content-container\">${(0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false, entriesToShow)}</div>`;\n    }\n    return `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderRegexView = (regexes, searchTerm, title, scope) => {\n    let content = `<div class=\"wio-regex-group\" data-scope=\"${scope}\"><h3>${title} (${regexes.length})</h3>`;\n    if (regexes.length === 0 && scope === 'character' && !(0,_store__WEBPACK_IMPORTED_MODULE_0__.getState)().character) {\n        content += `<p class=\"wio-info-text\">没有加载角色，无法显示角色正则。</p>`;\n    }\n    else if (regexes.length === 0) {\n        content += `<p class=\"wio-info-text\">没有找到正则。</p>`;\n    }\n    const filteredRegexes = searchTerm\n        ? regexes.filter(r => (r.script_name || '').toLowerCase().includes(searchTerm) ||\n            (r.find_regex || '').toLowerCase().includes(searchTerm) ||\n            (r.replace_string || '').toLowerCase().includes(searchTerm))\n        : regexes;\n    if (filteredRegexes.length > 0) {\n        content += `<div class=\"wio-content-container\">${filteredRegexes.map(r => (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createRegexItemElement)(r, searchTerm)).join('')}</div>`;\n    }\n    else if (searchTerm) {\n        content += `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的正则。</p>`;\n    }\n    if (scope === 'global' || (scope === 'character' && (0,_store__WEBPACK_IMPORTED_MODULE_0__.getState)().character)) {\n        content += `<div class=\"wio-regex-actions\"><button class=\"wio-create-regex-btn\" data-scope=\"${scope}\" aria-label=\"新建正则表达式\">+ 新建正则</button></div>`;\n    }\n    return content + '</div>';\n};\n// 辅助函数：获取当前视图的可访问性标签\nconst getViewAriaLabel = (viewName) => {\n    const labels = {\n        'global': '全局世界书视图',\n        'character': '角色世界书视图',\n        'chat': '聊天世界书视图',\n        'regex': '正则表达式视图'\n    };\n    return labels[viewName] || '世界书优化器视图';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/views.ts\n\n}");
  }
};

var __webpack_module_cache__ = {};

function __webpack_require__(moduleId) {
  var cachedModule = __webpack_module_cache__[moduleId];
  if (cachedModule !== undefined) {
    return cachedModule.exports;
  }
  var module = __webpack_module_cache__[moduleId] = {
    exports: {}
  };
  __webpack_modules__[moduleId](module, module.exports, __webpack_require__);
  return module.exports;
}

(() => {
  __webpack_require__.d = (exports, definition) => {
    for (var key in definition) {
      if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
        Object.defineProperty(exports, key, {
          enumerable: true,
          get: definition[key]
        });
      }
    }
  };
})();

(() => {
  __webpack_require__.o = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);
})();

(() => {
  __webpack_require__.r = exports => {
    if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
      Object.defineProperty(exports, Symbol.toStringTag, {
        value: "Module"
      });
    }
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
  };
})();

var __webpack_exports__ = __webpack_require__("./src/world_info_optimizer/index.ts");