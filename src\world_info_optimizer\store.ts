// src/world_info_optimizer/store.ts

import { AppState, TavernRegex, LorebookFile, LorebookEntry } from './types';
import { INITIAL_ACTIVE_TAB } from './constants';

// --- State Definition ---

const initialState: AppState = {
    regexes: { global: [], character: [] },
    lorebooks: { character: [] },
    chatLorebook: null,
    allLorebooks: [],
    lorebookEntries: new Map<string, LorebookEntry[]>(),
    lorebookUsage: new Map<string, string[]>(),
    activeTab: INITIAL_ACTIVE_TAB,
    isDataLoaded: false,
    searchFilters: {
        bookName: true,
        entryName: true,
        keywords: true,
        content: true,
    },
    searchQuery: '',
    multiSelectMode: false,
    selectedItems: new Set<string>(),
};

// 使用深拷贝来创建可变状态，避免直接修改initialState
let state: AppState = JSON.parse(JSON.stringify(initialState));
// Map和Set不能通过JSON.stringify/parse正确克隆，需要手动重新创建
state.lorebookEntries = new Map<string, LorebookEntry[]>();
state.lorebookUsage = new Map<string, string[]>();
state.selectedItems = new Set<string>();

// --- Listeners for State Changes ---

type Listener = (newState: AppState) => void;
const listeners: Listener[] = [];

/**
 * 订阅状态变化。
 * @param listener 当状态更新时要调用的回调函数。
 * @returns 一个取消订阅的函数。
 */
export const subscribe = (listener: Listener) => {
    listeners.push(listener);
    return () => {
        const index = listeners.indexOf(listener);
        if (index > -1) {
            listeners.splice(index, 1);
        }
    };
};

/**
 * 通知所有监听器状态已更新。
 */
const notify = () => {
    // 传递状态的深拷贝，以防监听器意外修改状态
    const deepCopiedState = {
        ...state,
        lorebookEntries: new Map(state.lorebookEntries),
        lorebookUsage: new Map(state.lorebookUsage),
        selectedItems: new Set(state.selectedItems),
    };
    listeners.forEach(listener => listener(deepCopiedState));
};

// --- State Accessors and Mutators ---

/**
 * 获取当前状态对象的快照。
 * @returns 当前应用状态的深拷贝。
 */
export const getState = (): AppState => {
    // 返回深拷贝以保证状态的不可变性
    return {
        ...state,
        lorebookEntries: new Map(state.lorebookEntries),
        lorebookUsage: new Map(state.lorebookUsage),
        selectedItems: new Set(state.selectedItems),
    };
};

/**
 * 更新应用状态并通知所有订阅者。
 * @param updater 一个函数，接收当前状态并返回一个新的（或修改过的）状态部分。
 */
export const updateState = (updater: (currentState: AppState) => Partial<AppState>) => {
    const updates = updater(state);
    state = { ...state, ...updates };
    notify();
};

/**
 * 重置整个应用状态到初始值。
 */
export const resetState = () => {
    state = JSON.parse(JSON.stringify(initialState));
    state.lorebookEntries = new Map<string, LorebookEntry[]>();
    state.lorebookUsage = new Map<string, string[]>();
    state.selectedItems = new Set<string>();
    notify();
};

// --- Specific State Updater Functions ---

export const setActiveTab = (tabId: string) => {
    updateState(s => ({ ...s, activeTab: tabId }));
};

export const setDataLoaded = (isLoaded: boolean) => {
    updateState(s => ({ ...s, isDataLoaded: isLoaded }));
};

export const setSearchQuery = (query: string) => {
    updateState(s => ({ ...s, searchQuery: query }));
};

export const setAllData = (data: {
    globalRegexes: TavernRegex[],
    characterRegexes: TavernRegex[],
    allLorebooks: LorebookFile[],
    characterLorebooks: string[],
    chatLorebook: string | null,
    lorebookEntries: Map<string, LorebookEntry[]>,
    lorebookUsage: Map<string, string[]>
}) => {
    updateState(s => ({
        ...s,
        regexes: { global: data.globalRegexes, character: data.characterRegexes },
        allLorebooks: data.allLorebooks,
        lorebooks: { character: data.characterLorebooks },
        chatLorebook: data.chatLorebook,
        lorebookEntries: data.lorebookEntries,
        lorebookUsage: data.lorebookUsage,
        isDataLoaded: true,
    }));
};