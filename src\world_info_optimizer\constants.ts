// src/world_info_optimizer/constants.ts

// --- UI Element IDs ---
export const PANEL_ID = 'world-info-optimizer-panel';
export const BUTTON_ID = 'world-info-optimizer-button';
export const SEARCH_INPUT_ID = 'wio-search-input';
export const REFRESH_BTN_ID = 'wio-refresh-btn';
export const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';
export const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';
export const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';

// --- UI Display Text & URLs ---
export const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';
export const BUTTON_TOOLTIP = '世界书 & 正则便捷管理 (WIO)';
export const BUTTON_TEXT_IN_MENU = '世界书 & 正则便捷管理 (WIO)';

// --- Lorebook Configuration Options ---
export const LOREBOOK_INSERTION_POSITIONS = {
    'before_character_definition': '角色定义前',
    'after_character_definition': '角色定义后',
    'before_example_messages': '聊天示例前',
    'after_example_messages': '聊天示例后',
    'before_author_note': '作者笔记前',
    'after_author_note': '作者笔记后',
    'at_depth_as_system': '@D ⚙ 系统',
    'at_depth_as_assistant': '@D 🗨️ 角色',
    'at_depth_as_user': '@D 👤 用户'
};

export const LOREBOOK_LOGIC_OPTIONS = {
    'and_any': '任一 AND',
    'and_all': '所有 AND',
    'not_any': '任一 NOT',
    'not_all': '所有 NOT'
};

// --- Other Constants ---
export const INITIAL_ACTIVE_TAB = 'global-lore';