// src/world_info_optimizer/index.ts

import { injectUI, initUI } from './ui';
import { initializeEventHandlers } from './events';
import { setupResponsiveResizeHand<PERSON>, applyHighContrastMode, applyReducedMotionMode } from './ui/helpers';

/**
 * 等待SillyTavern的核心DOM和API准备就绪。
 * @param callback 准备就绪后执行的回调函数
 */
function onReady(callback: (parentWindow: Window) => void) {
    const domSelector = '#extensionsMenu';
    const maxRetries = 100; // 等待最多约20秒
    let retries = 0;

    console.log('[WIO] Starting readiness check...');

    const interval = setInterval(() => {
        const parentWin = window.parent;
        if (!parentWin) {
            retries++;
            return;
        }

        const domReady = parentWin.document.querySelector(domSelector) !== null;
        const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;

        if (domReady && apiReady) {
            clearInterval(interval);
            console.log('[WIO] SUCCESS: DOM and Core APIs are ready. Initializing script.');
            try {
                callback(parentWin);
            } catch (e) {
                console.error('[WIO] FATAL: Error during main callback execution.', e);
            }
        } else {
            retries++;
            if (retries > maxRetries) {
                clearInterval(interval);
                console.error('[WIO] FATAL: Readiness check timed out.');
                if (!domReady) console.error(`[WIO] -> Failure: DOM element "${domSelector}" not found.`);
                if (!apiReady) console.error(`[WIO] -> Failure: Core APIs not available. TavernHelper: ${!!parentWin.TavernHelper}, jQuery: ${!!parentWin.jQuery}`);
            }
        }
    }, 200);
}

/**
 * 应用程序的主函数。
 * @param parentWindow 父窗口对象
 */
function main(parentWindow: Window) {
    console.log('[WIO] Initializing World Info Optimizer...');

    // 1. 注入UI元素到DOM中
    injectUI(parentWindow);

    // 2. 初始化UI模块，使其订阅状态变化
    initUI();
    
    // 3. 绑定所有事件处理器
    initializeEventHandlers(parentWindow);

    // 4. 设置响应式设计支持
    setupResponsiveResizeHandler();
    applyHighContrastMode();
    applyReducedMotionMode();

    console.log('[WIO] World Info Optimizer initialized successfully.');
}

// --- 脚本启动 ---
(() => {
    // 使用IIFE封装，避免全局污染
    console.log('[WIO Script] Execution started.');
    onReady(main);
})();