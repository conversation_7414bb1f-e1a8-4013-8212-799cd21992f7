// src/world_info_optimizer/ui/helpers.ts

export const escapeHtml = (text: any): string => {
    if (typeof text !== 'string') text = String(text);
    const p = document.createElement('p');
    p.textContent = text;
    return p.innerHTML;
};

export const highlightText = (text: string, searchTerm: string): string => {
    if (!searchTerm || !text) return escapeHtml(text);
    const escapedText = escapeHtml(text);
    const htmlSafeSearchTerm = escapeHtml(searchTerm);
    const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');
    return escapedText.replace(regex, '<mark class="wio-search-highlight">$1</mark>');
};

/**
 * 获取响应式CSS类名
 * @param componentType 组件类型
 * @returns 响应式CSS类名字符串
 */
export const getResponsiveClass = (componentType: string): string => {
    const responsiveClasses: Record<string, string> = {
        'modal': 'wio-modal-content-responsive',
        'panel': 'wio-panel-responsive',
        'button': 'wio-button-responsive',
        'form-group': 'wio-form-group-responsive',
        'input': 'wio-input-responsive',
        'list': 'wio-list-responsive'
    };
    return responsiveClasses[componentType] || '';
};

/**
 * 检查当前屏幕尺寸
 * @returns 当前屏幕尺寸类型
 */
export const getScreenSize = (): 'small' | 'medium' | 'large' => {
    const width = window.innerWidth;
    if (width < 768) return 'small';
    if (width < 1200) return 'medium';
    return 'large';
};

/**
 * 判断是否需要显示移动端视图
 * @returns 是否为移动视图
 */
export const isMobileView = (): boolean => {
    return getScreenSize() === 'small';
};

/**
 * 为元素添加响应式调整事件
 * @param element 目标元素
 * @param callback 响应式调整回调函数
 */
export const addResponsiveListener = (element: HTMLElement, callback: (screenSize: 'small' | 'medium' | 'large') => void): void => {
    const handleResize = () => {
        const screenSize = getScreenSize();
        callback(screenSize);
    };
    
    window.addEventListener('resize', handleResize);
    // 立即执行一次回调以应用初始状态
    handleResize();
    
    // 添加清理函数到元素上，便于后续移除事件监听器
    (element as any)._responsiveCleanup = () => {
        window.removeEventListener('resize', handleResize);
    };
};

/**
 * 移除响应式调整事件
 * @param element 目标元素
 */
export const removeResponsiveListener = (element: HTMLElement): void => {
    if ((element as any)._responsiveCleanup) {
        (element as any)._responsiveCleanup();
        delete (element as any)._responsiveCleanup;
    }
};

/**
 * 设置全局响应式尺寸调整处理器
 */
export const setupResponsiveResizeHandler = (): void => {
    let currentSize = getScreenSize();
    
    const handleResize = () => {
        const newSize = getScreenSize();
        if (newSize !== currentSize) {
            currentSize = newSize;
            
            // 触发自定义响应式调整事件
            const event = new CustomEvent('wio-responsive-resize', {
                detail: { screenSize: newSize }
            });
            document.dispatchEvent(event);
            
            console.log(`[WIO] Screen size changed to: ${newSize}`);
        }
    };
    
    window.addEventListener('resize', handleResize);
    console.log('[WIO] Responsive resize handler initialized.');
};

/**
 * 应用高对比度模式
 */
export const applyHighContrastMode = (): void => {
    // 检查用户是否有高对比度偏好
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    
    const updateContrastMode = (e: MediaQueryListEvent) => {
        if (e.matches) {
            document.documentElement.classList.add('wio-high-contrast');
            console.log('[WIO] High contrast mode enabled.');
        } else {
            document.documentElement.classList.remove('wio-high-contrast');
            console.log('[WIO] High contrast mode disabled.');
        }
    };
    
    // 初始应用
    if (mediaQuery.matches) {
        document.documentElement.classList.add('wio-high-contrast');
    }
    
    // 监听变化
    mediaQuery.addEventListener('change', updateContrastMode);
    console.log('[WIO] High contrast mode support initialized.');
};

/**
 * 应用减少动画模式
 */
export const applyReducedMotionMode = (): void => {
    // 检查用户是否有减少动画偏好
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const updateMotionMode = (e: MediaQueryListEvent) => {
        if (e.matches) {
            document.documentElement.classList.add('wio-reduced-motion');
            console.log('[WIO] Reduced motion mode enabled.');
        } else {
            document.documentElement.classList.remove('wio-reduced-motion');
            console.log('[WIO] Reduced motion mode disabled.');
        }
    };
    
    // 初始应用
    if (mediaQuery.matches) {
        document.documentElement.classList.add('wio-reduced-motion');
    }
    
    // 监听变化
    mediaQuery.addEventListener('change', updateMotionMode);
    console.log('[WIO] Reduced motion mode support initialized.');
};