// src/world_info_optimizer/ui.ts

import { getState, subscribe } from './store';
import {
    PANEL_ID, BUTTON_ID, SEARCH_INPUT_ID, REFRESH_BTN_ID, CREATE_LOREBOOK_BTN_ID,
    BUTTON_ICON_URL, BUTTON_TEXT_IN_MENU
} from './constants';
import {
    renderGlobalLorebookView,
    renderCharacterLorebookView,
    renderChatLorebookView,
    renderRegexView
} from './ui/views';
import { showModal } from './ui/modals';
import { getScreenSize, addResponsiveListener } from './ui/helpers';

// --- Private Variables ---
let parentDoc: Document;
let $: JQueryStatic;

// --- Main Panel and Button Injection ---

export const injectUI = (parentWindow: Window) => {
    parentDoc = parentWindow.document;
    $ = parentWindow.jQuery;

    if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {
        console.log('[WIO] UI already injected.');
        return;
    }

    // 使用CSS变量定义主题颜色和间距，便于统一管理和响应式调整
    const styles = `
        /* CSS变量定义 */
        :root {
            --wio-bg-primary: #333;
            --wio-bg-secondary: #444;
            --wio-bg-tertiary: #2a2a2a;
            --wio-bg-toolbar: #3a3a3a;
            --wio-text-primary: #eee;
            --wio-text-secondary: #ccc;
            --wio-border-color: #555;
            --wio-highlight-color: #00aaff;
            --wio-border-radius: 8px;
            --wio-spacing-xs: 4px;
            --wio-spacing-sm: 8px;
            --wio-spacing-md: 10px;
            --wio-spacing-lg: 15px;
            --wio-font-size-sm: 12px;
            --wio-font-size-md: 14px;
            --wio-font-size-lg: 16px;
            --wio-shadow: 0 5px 15px rgba(0,0,0,0.5);
        }

        /* 基础面板样式 */
        #${PANEL_ID} {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
            overflow: hidden;
        }
        
        #${PANEL_ID} .wio-panel-inner {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
            background-color: var(--wio-bg-primary);
            color: var(--wio-text-primary);
        }

        /* 响应式面板调整 */
        @media (min-width: 768px) {
            #${PANEL_ID} {
                top: 50%;
                left: 50%;
                right: auto;
                bottom: auto;
                transform: translate(-50%, -50%);
                width: 80%;
                max-width: 900px;
                height: 70%;
                border: 1px solid var(--wio-border-color);
                border-radius: var(--wio-border-radius);
                box-shadow: var(--wio-shadow);
            }
            
            #${PANEL_ID} .wio-panel-inner {
                height: 100%;
                width: 100%;
            }
        }

        /* 头部样式 */
        .wio-header {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            background-color: var(--wio-bg-secondary);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--wio-border-color);
            flex-shrink: 0;
        }
        
        .wio-header h2 {
            margin: 0;
            font-size: var(--wio-font-size-lg);
        }
        
        .wio-close-btn {
            background: none;
            border: none;
            color: var(--wio-text-primary);
            font-size: 24px;
            cursor: pointer;
            padding: var(--wio-spacing-xs);
            display: flex;
            align-items: center;
            justify-content: center;
        }        
        
        /* 选项卡样式 */
        .wio-tabs {
            display: flex;
            background-color: var(--wio-bg-tertiary);
            overflow-x: auto;
            white-space: nowrap;
            flex-shrink: 0;
            border-bottom: 1px solid var(--wio-border-color);
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .wio-tabs::-webkit-scrollbar {
            display: none;
        }
        
        .wio-tab-btn {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            border: none;
            background-color: transparent;
            color: var(--wio-text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            flex-shrink: 0;
            font-size: var(--wio-font-size-md);
            transition: all 0.2s ease;
            outline: none;
        }
        
        .wio-tab-btn.active {
            color: var(--wio-text-primary);
            border-bottom-color: var(--wio-highlight-color);
            background-color: rgba(0, 170, 255, 0.1);
        }
        
        .wio-tab-btn:focus {
            outline: 2px solid var(--wio-highlight-color);
            outline-offset: 2px;
        }
        
        /* 工具栏样式 */
        .wio-toolbar {
            padding: var(--wio-spacing-md);
            display: flex;
            gap: var(--wio-spacing-md);
            background-color: var(--wio-bg-toolbar);
            border-bottom: 1px solid var(--wio-border-color);
            flex-shrink: 0;
        }
        
        #${SEARCH_INPUT_ID} {
            flex-grow: 1;
            padding: var(--wio-spacing-md);
            border: 1px solid var(--wio-border-color);
            border-radius: var(--wio-border-radius);
            background-color: var(--wio-bg-primary);
            color: var(--wio-text-primary);
            font-size: var(--wio-font-size-md);
            outline: none;
        }
        
        #${SEARCH_INPUT_ID}::placeholder {
            color: var(--wio-text-secondary);
        }
        
        #${SEARCH_INPUT_ID}:focus {
            border-color: var(--wio-highlight-color);
            box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.2);
        }
        
        .wio-toolbar button {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            border: 1px solid var(--wio-border-color);
            border-radius: var(--wio-border-radius);
            background-color: var(--wio-bg-secondary);
            color: var(--wio-text-primary);
            cursor: pointer;
            font-size: var(--wio-font-size-md);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--wio-spacing-xs);
            transition: all 0.2s ease;
            outline: none;
            min-width: 44px;
            min-height: 44px;
        }
        
        .wio-toolbar button:hover {
            background-color: var(--wio-highlight-color);
            border-color: var(--wio-highlight-color);
        }
        
        .wio-toolbar button:focus {
            outline: 2px solid var(--wio-highlight-color);
            outline-offset: 2px;
        }

        /* 主内容区域样式 */
        .wio-main-content {
            flex-grow: 1;
            overflow-y: auto;
            padding: var(--wio-spacing-lg);
        }
        
        /* 列表样式 */
        .wio-book-group {
            margin-bottom: var(--wio-spacing-lg);
            border: 1px solid var(--wio-border-color);
            border-radius: var(--wio-border-radius);
            overflow: hidden;
        }
        
        .wio-book-header {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            background-color: var(--wio-bg-secondary);
            display: flex;
            align-items: center;
            gap: var(--wio-spacing-md);
            border-bottom: 1px solid var(--wio-border-color);
        }
        
        .wio-book-header h4 {
            margin: 0;
            flex-grow: 1;
            font-size: var(--wio-font-size-lg);
        }
        
        .wio-usage-pill {
            padding: 2px 8px;
            background-color: var(--wio-highlight-color);
            color: white;
            border-radius: 12px;
            font-size: var(--wio-font-size-sm);
        }
        
        .wio-item-controls {
            display: flex;
            gap: var(--wio-spacing-xs);
        }
        
        .wio-item-controls button {
            padding: var(--wio-spacing-xs) var(--wio-spacing-sm);
            background-color: transparent;
            border: 1px solid var(--wio-border-color);
            border-radius: var(--wio-border-radius);
            color: var(--wio-text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .wio-item-controls button:hover {
            background-color: var(--wio-bg-tertiary);
        }
        
        .wio-entry-list {
            background-color: var(--wio-bg-primary);
        }
        
        .wio-entry-item {
            padding: var(--wio-spacing-md);
            border-bottom: 1px solid var(--wio-border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .wio-entry-item:last-child {
            border-bottom: none;
        }
        
        .wio-entry-main {
            display: flex;
            align-items: center;
            gap: var(--wio-spacing-md);
            flex-grow: 1;
        }
        
        .wio-entry-name {
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .wio-entry-keys {
            font-size: var(--wio-font-size-sm);
            color: var(--wio-text-secondary);
            font-style: italic;
            flex-grow: 1;
            word-break: break-word;
        }
        
        .wio-entry-actions,
        .wio-regex-actions {
            padding: var(--wio-spacing-md);
            text-align: center;
            background-color: var(--wio-bg-tertiary);
        }
        
        .wio-create-entry-btn,
        .wio-create-regex-btn {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            background-color: var(--wio-highlight-color);
            border: none;
            border-radius: var(--wio-border-radius);
            color: white;
            cursor: pointer;
            font-size: var(--wio-font-size-md);
        }

        /* 正则表达式样式 */
        .wio-regex-group {
            margin-bottom: var(--wio-spacing-lg);
            border: 1px solid var(--wio-border-color);
            border-radius: var(--wio-border-radius);
            overflow: hidden;
        }
        
        .wio-regex-group h3 {
            padding: var(--wio-spacing-md) var(--wio-spacing-lg);
            margin: 0;
            background-color: var(--wio-bg-secondary);
            border-bottom: 1px solid var(--wio-border-color);
        }
        
        .wio-regex-item {
            padding: var(--wio-spacing-md);
            border-bottom: 1px solid var(--wio-border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .wio-regex-item:last-child {
            border-bottom: none;
        }
        
        .wio-regex-main {
            display: flex;
            align-items: center;
            gap: var(--wio-spacing-md);
            flex-grow: 1;
            flex-wrap: wrap;
        }
        
        .wio-regex-name {
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .wio-regex-find,
        .wio-regex-replace {
            background-color: var(--wio-bg-tertiary);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: var(--wio-font-size-sm);
            word-break: break-all;
        }
        
        .wio-info-text {
            text-align: center;
            color: var(--wio-text-secondary);
            font-style: italic;
            padding: var(--wio-spacing-lg);
        }
        
        .wio-search-highlight {
            background-color: rgba(255, 255, 0, 0.3);
            padding: 0 2px;
            border-radius: 2px;
        }

        /* 页脚样式 */
        .wio-footer {
            padding: var(--wio-spacing-sm) var(--wio-spacing-lg);
            background-color: var(--wio-bg-tertiary);
            font-size: var(--wio-font-size-sm);
            text-align: right;
            border-top: 1px solid var(--wio-border-color);
            flex-shrink: 0;
        }

        /* 复选框样式优化 */
        input[type="checkbox"] {
            transform: scale(1.2);
            accent-color: var(--wio-highlight-color);
            margin-right: var(--wio-spacing-xs);
        }
        
        /* 聚焦样式优化 */
        :focus-visible {
            outline: 2px solid var(--wio-highlight-color);
            outline-offset: 2px;
        }

        /* 滚动条样式 */
        .wio-main-content::-webkit-scrollbar,
        .wio-tabs::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        .wio-main-content::-webkit-scrollbar-track,
        .wio-tabs::-webkit-scrollbar-track {
            background: var(--wio-bg-tertiary);
        }
        
        .wio-main-content::-webkit-scrollbar-thumb,
        .wio-tabs::-webkit-scrollbar-thumb {
            background: var(--wio-border-color);
            border-radius: 4px;
        }
        
        .wio-main-content::-webkit-scrollbar-thumb:hover,
        .wio-tabs::-webkit-scrollbar-thumb:hover {
            background: #777;
        }

        /* 媒体查询：小型设备优化 */
        @media (max-width: 767px) {
            :root {
                --wio-spacing-xs: 2px;
                --wio-spacing-sm: 6px;
                --wio-spacing-md: 8px;
                --wio-spacing-lg: 10px;
                --wio-font-size-sm: 11px;
                --wio-font-size-md: 13px;
                --wio-font-size-lg: 15px;
            }
            
            .wio-header h2 {
                font-size: var(--wio-font-size-md);
            }
            
            .wio-entry-main {
                flex-wrap: wrap;
            }
            
            .wio-entry-name {
                flex-basis: 100%;
                margin-bottom: var(--wio-spacing-xs);
            }
            
            .wio-regex-main {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--wio-spacing-xs);
            }
            
            .wio-regex-name,
            .wio-regex-find,
            .wio-regex-replace {
                width: 100%;
                box-sizing: border-box;
            }
            
            .wio-toolbar {
                flex-wrap: wrap;
            }
            
            #${SEARCH_INPUT_ID} {
                width: 100%;
            }
            
            /* 触摸目标优化 */
            button,
            input[type="checkbox"] {
                touch-action: manipulation;
            }
        }
        
        /* 平板设备优化 */
        @media (min-width: 768px) and (max-width: 1024px) {
            #${PANEL_ID} {
                width: 90%;
                height: 80%;
            }
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            :root {
                --wio-border-color: #fff;
                --wio-bg-primary: #000;
                --wio-bg-secondary: #333;
                --wio-bg-tertiary: #222;
                --wio-bg-toolbar: #444;
                --wio-text-primary: #fff;
                --wio-text-secondary: #ddd;
                --wio-highlight-color: #ff0;
            }
        }

        /* 减少动画模式支持 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    `;
    const styleSheet = parentDoc.createElement("style");
    styleSheet.type = "text/css";
    styleSheet.innerText = styles;
    parentDoc.head.appendChild(styleSheet);

    const panelHtml = `
        <div id="${PANEL_ID}">
            <div class="wio-panel-inner">
                <div class="wio-header">
                    <h2>世界书 & 正则便捷管理 (WIO)</h2>
                    <button class="wio-close-btn" aria-label="关闭面板">&times;</button>
                </div>
                <div class="wio-tabs" role="tablist">
                    <button class="wio-tab-btn" data-tab-id="global-lore" role="tab" aria-controls="global-lore-content" aria-selected="false">全局世界书</button>
                    <button class="wio-tab-btn" data-tab-id="char-lore" role="tab" aria-controls="char-lore-content" aria-selected="false">角色世界书</button>
                    <button class="wio-tab-btn" data-tab-id="chat-lore" role="tab" aria-controls="chat-lore-content" aria-selected="false">聊天世界书</button>
                    <button class="wio-tab-btn" data-tab-id="global-regex" role="tab" aria-controls="global-regex-content" aria-selected="false">全局正则</button>
                    <button class="wio-tab-btn" data-tab-id="char-regex" role="tab" aria-controls="char-regex-content" aria-selected="false">角色正则</button>
                </div>
                <div class="wio-toolbar">
                    <input type="search" id="${SEARCH_INPUT_ID}" placeholder="搜索..." aria-label="搜索内容">
                    <button id="${REFRESH_BTN_ID}" title="刷新数据" aria-label="刷新数据"><i class="fa-solid fa-sync"></i></button>
                    <button id="${CREATE_LOREBOOK_BTN_ID}" title="新建世界书" aria-label="新建世界书"><i class="fa-solid fa-plus"></i></button>
                </div>
                <div class="wio-main-content" role="main" id="tab-content-container"></div>
                <div class="wio-footer">
                    <span>WIO v3.0 (Refactored)</span>
                </div>
            </div>
        </div>
    `;
    $('body', parentDoc).append(panelHtml);

    const extensionButton = `
        <div id="${BUTTON_ID}" class="list-group-item">
            <img src="${BUTTON_ICON_URL}" style="width: 24px; height: 24px; margin-right: 10px;">
            <span>${BUTTON_TEXT_IN_MENU}</span>
        </div>
    `;
    $('#extensions_list', parentDoc).append(extensionButton);
    
    // 添加键盘导航支持
    addKeyboardNavigation();
    
    // 添加响应式布局监听器
    addResponsiveListener(parentWindow, () => {
        updatePanelLayout();
    });
    
    console.log('[WIO] UI Injected successfully.');
};

// --- Utility Functions ---

// 添加键盘导航支持
const addKeyboardNavigation = () => {
    if (!parentDoc) return;
    
    const panel = parentDoc.getElementById(PANEL_ID);
    const closeBtn = panel?.querySelector('.wio-close-btn') as HTMLButtonElement;
    const searchInput = panel?.querySelector(`#${SEARCH_INPUT_ID}`) as HTMLInputElement;
    const tabButtons = panel?.querySelectorAll('.wio-tab-btn');
    
    if (!panel || !closeBtn || !searchInput || !tabButtons) return;
    
    // 监听全局键盘事件
    panel.addEventListener('keydown', (e) => {
        // ESC键关闭面板（当没有模态框打开时）
        if (e.key === 'Escape') {
            if (!parentDoc.querySelector('.swal2-container')) {
                closeBtn.click();
            }
        }
        
        // F1键显示帮助
        if (e.key === 'F1') {
            e.preventDefault();
            showModal({
                type: 'alert',
                title: '帮助',
                text: '世界书优化器快捷键:\n- Tab: 导航到下一个元素\n- Shift+Tab: 导航到上一个元素\n- Enter: 确认操作\n- Escape: 关闭面板或取消操作\n- Ctrl+F: 聚焦搜索框\n- F1: 显示此帮助信息'
            });
        }
        
        // Ctrl+F 聚焦搜索框
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            searchInput.focus();
            searchInput.select();
        }
        
        // Ctrl+Shift+Tab / Ctrl+Tab 在标签页之间导航
        if (e.ctrlKey && (e.key === 'Tab' || e.key === 'Shift')) {
            if (e.key === 'Tab') {
                e.preventDefault();
                const activeIndex = Array.from(tabButtons).findIndex(btn => 
                    btn.classList.contains('active')
                );
                const nextIndex = e.shiftKey 
                    ? (activeIndex - 1 + tabButtons.length) % tabButtons.length
                    : (activeIndex + 1) % tabButtons.length;
                (tabButtons[nextIndex] as HTMLElement).click();
                (tabButtons[nextIndex] as HTMLElement).focus();
            }
        }
    });
};

// 更新面板布局
const updatePanelLayout = () => {
    if (!parentDoc) return;
    
    const panel = parentDoc.getElementById(PANEL_ID);
    if (!panel) return;
    
    const screenSize = getScreenSize();
    
    // 根据屏幕尺寸调整UI元素
    if (screenSize === 'small') {
        // 移动端特有调整
    } else if (screenSize === 'medium') {
        // 平板端特有调整
    } else {
        // 桌面端特有调整
    }
};

// --- Core Render Logic ---

const render = () => {
    if (!parentDoc) return;

    const state = getState();
    const $panel = $(`#${PANEL_ID}`, parentDoc);
    if (!$panel.length) return;

    $panel.find('.wio-tab-btn').removeClass('active');
    $panel.find(`.wio-tab-btn[data-tab-id="${state.activeTab}"]`).addClass('active');

    const $mainContent = $panel.find('.wio-main-content');
    if (!state.isDataLoaded) {
        $mainContent.html('<p class="wio-info-text">正在加载数据...</p>');
        return;
    }

    const searchTerm = state.searchQuery.toLowerCase();
    const $searchInput = $(`#${SEARCH_INPUT_ID}`, parentDoc);
    if ($searchInput.val() !== state.searchQuery) {
        $searchInput.val(state.searchQuery);
    }

    switch (state.activeTab) {
        case 'global-lore':
            $mainContent.html(renderGlobalLorebookView(state, searchTerm));
            break;
        case 'char-lore':
            $mainContent.html(renderCharacterLorebookView(state, searchTerm));
            break;
        case 'chat-lore':
            $mainContent.html(renderChatLorebookView(state, searchTerm));
            break;
        case 'global-regex':
            $mainContent.html(renderRegexView(state.regexes.global, searchTerm, '全局正则', 'global'));
            break;
        case 'char-regex':
            $mainContent.html(renderRegexView(state.regexes.character, searchTerm, '角色正则', 'character'));
            break;
        default:
            $mainContent.html(`<p>未知视图: ${state.activeTab}</p>`);
    }
};

// --- UI Initialization ---

export const initUI = () => {
    subscribe(render);
};