// src/world_info_optimizer/events.ts

import { PANEL_ID, BUTTON_ID, CREATE_LOREBOOK_BTN_ID, REFRESH_BTN_ID, SEARCH_INPUT_ID } from './constants';
import {
    loadAllData,
    createLorebook,
    renameLorebook,
    deleteLorebook,
    createLorebookEntry,
    updateLorebookEntry,
    deleteLorebookEntry,
    setGlobalLorebookEnabled,
    createRegex,
    updateRegex,
    deleteRegex
} from './core';
import { setActiveTab, getState, setSearchQuery } from './store';
import { showModal, showEntryEditorModal, showRegexEditorModal } from './ui';

let parentDoc: Document;
let $: JQueryStatic;

/**
 * 初始化所有UI事件处理器。
 * 这个函数应该在UI注入DOM后调用。
 * @param parentWindow 父窗口对象
 */
export const initializeEventHandlers = (parentWindow: Window) => {
    parentDoc = parentWindow.document;
    $ = parentWindow.jQuery;
    const $body = $('body', parentDoc);

    // --- 主面板和按钮的事件 ---

    // 打开面板的按钮
    $body.on('click', `#${BUTTON_ID}`, () => {
        $(`#${PANEL_ID}`, parentDoc).fadeIn(200);
        // 首次打开时加载数据
        if (!getState().isDataLoaded) {
            loadAllData();
        }
    });

    // 关闭面板的按钮
    $body.on('click', `#${PANEL_ID} .wio-close-btn`, () => {
        $(`#${PANEL_ID}`, parentDoc).fadeOut(200);
    });

    // --- 事件委托：主面板内的所有点击事件 ---
    const $panel = $(`#${PANEL_ID}`, parentDoc);

    $panel.on('click', async (event) => {
        const $target = $(event.target);
        
        // --- 标签页切换 ---
        const tabButton = $target.closest('.wio-tab-btn');
        if (tabButton.length) {
            const tabId = tabButton.data('tab-id');
            setActiveTab(tabId);
            return;
        }

        // --- 工具栏操作 ---
        if ($target.closest(`#${CREATE_LOREBOOK_BTN_ID}`).length) {
            handleCreateBook();
            return;
        }
        if ($target.closest(`#${REFRESH_BTN_ID}`).length) {
            loadAllData();
            return;
        }

        // --- 世界书操作 ---
        const renameBookBtn = $target.closest('.wio-rename-book-btn');
        if (renameBookBtn.length) {
            const bookName = renameBookBtn.closest('.wio-book-group').data('book-name');
            handleRenameBook(bookName);
            return;
        }

        const deleteBookBtn = $target.closest('.wio-delete-book-btn');
        if (deleteBookBtn.length) {
            const bookName = deleteBookBtn.closest('.wio-book-group').data('book-name');
            handleDeleteBook(bookName);
            return;
        }

        // --- 条目操作 ---
        const createEntryBtn = $target.closest('.wio-create-entry-btn');
        if (createEntryBtn.length) {
            const bookName = createEntryBtn.data('book-name');
            handleCreateEntry(bookName);
            return;
        }
        
        const editEntryBtn = $target.closest('.wio-edit-entry-btn');
        if (editEntryBtn.length) {
            const entryItem = editEntryBtn.closest('.wio-entry-item');
            const bookName = entryItem.data('book-name');
            const uid = entryItem.data('uid');
            handleEditEntry(bookName, uid);
            return;
        }

        const deleteEntryBtn = $target.closest('.wio-delete-entry-btn');
        if (deleteEntryBtn.length) {
            const entryItem = deleteEntryBtn.closest('.wio-entry-item');
            const bookName = entryItem.data('book-name');
            const uid = entryItem.data('uid');
            handleDeleteEntry(bookName, uid);
            return;
        }

        // --- 正则操作 ---
        const createRegexBtn = $target.closest('.wio-create-regex-btn');
        if (createRegexBtn.length) {
            const scope = createRegexBtn.data('scope');
            handleCreateRegex(scope);
            return;
        }

        const editRegexBtn = $target.closest('.wio-edit-regex-btn');
        if (editRegexBtn.length) {
            const regexItem = editRegexBtn.closest('.wio-regex-item');
            const regexId = regexItem.data('id');
            handleEditRegex(regexId);
            return;
        }

        const deleteRegexBtn = $target.closest('.wio-delete-regex-btn');
        if (deleteRegexBtn.length) {
            const regexItem = deleteRegexBtn.closest('.wio-regex-item');
            const regexId = regexItem.data('id');
            handleDeleteRegex(regexId);
            return;
        }
    });

    // --- 事件委托：处理表单元素的 change 事件 ---
    $panel.on('change', async (event) => {
        const $target = $(event.target);

        // --- 全局世界书启用/禁用切换 ---
        if ($target.is('.wio-global-book-toggle')) {
            const bookName = $target.closest('.wio-book-group').data('book-name');
            const isEnabled = $target.prop('checked');
            await setGlobalLorebookEnabled(bookName, isEnabled);
            return;
        }
    });

    // --- 事件委托：处理搜索框输入 ---
    $panel.on('input', `#${SEARCH_INPUT_ID}`, (event) => {
        const query = $(event.target).val() as string;
        setSearchQuery(query);
    });
};


// --- 事件处理器具体实现 ---

/**
 * 处理重命名世界书的逻辑。
 * @param bookName 要重命名的世界书的当前名称
 */
const handleRenameBook = async (bookName: string) => {
    try {
        const newName = await showModal({
            type: 'prompt',
            title: '重命名世界书',
            text: `为 "${bookName}" 输入新的名称:`,
            value: bookName,
        });

        if (typeof newName === 'string' && newName.trim() && newName !== bookName) {
            await renameLorebook(bookName, newName.trim());
        }
    } catch (error) {
        // 用户取消了输入
        console.log('Rename operation cancelled.');
    }
};

/**
 * 处理删除世界书的逻辑。
 * @param bookName 要删除的世界书的名称
 */
const handleDeleteBook = async (bookName: string) => {
    try {
        const confirmation = await showModal({
            type: 'confirm',
            title: '确认删除',
            text: `你确定要永久删除世界书 "${bookName}" 吗？此操作无法撤销。`,
        });

        if (confirmation) {
            await deleteLorebook(bookName);
        }
    } catch (error) {
        // 用户取消了确认
        console.log('Delete operation cancelled.');
    }
};

/**
 * 处理创建新条目的逻辑。
 * @param bookName 新条目所属的世界书名称
 */
const handleCreateEntry = async (bookName: string) => {
    try {
        const newEntryData = await showEntryEditorModal({
            entry: { keys: [], content: '', comment: '' },
            bookName,
            isCreating: true,
        });
        
        // showEntryEditorModal 返回的 newEntryData 不包含 enabled 状态, 我们需要设置默认值
        const entryToCreate = {
            ...newEntryData,
            enabled: true, // 新条目默认启用
        };

        await createLorebookEntry(bookName, entryToCreate);

    } catch (error) {
        console.log('Create entry operation cancelled.');
    }
};

/**
 * 处理编辑现有条目的逻辑。
 * @param bookName 条目所属的世界书名称
 * @param uid 要编辑的条目的UID
 */
const handleEditEntry = async (bookName: string, uid: string) => {
    const state = getState();
    const entry = state.lorebookEntries.get(bookName)?.find(e => e.uid === uid);

    if (!entry) {
        console.error(`Entry with UID ${uid} not found in book ${bookName}.`);
        return;
    }

    try {
        const updatedEntryData = await showEntryEditorModal({
            entry: { ...entry }, // 传入副本以防意外修改
            bookName,
            isCreating: false,
        });

        // 我们只需要更新发生变化的部分
        const updates = {
            comment: updatedEntryData.comment,
            keys: updatedEntryData.keys,
            content: updatedEntryData.content,
        };

        await updateLorebookEntry(bookName, uid, updates);

    } catch (error) {
        console.log('Edit entry operation cancelled.');
    }
};

/**
 * 处理删除条目的逻辑。
 * @param bookName 条目所属的世界书名称
 * @param uid 要删除的条目的UID
 */
const handleDeleteEntry = async (bookName: string, uid: string) => {
    try {
        const confirmation = await showModal({
            type: 'confirm',
            title: '确认删除',
            text: `你确定要永久删除这个条目吗？`,
        });

        if (confirmation) {
            await deleteLorebookEntry(bookName, uid);
        }
    } catch (error) {
        console.log('Delete entry operation cancelled.');
    }
};

/**
 * 处理创建新世界书的逻辑。
 */
const handleCreateBook = async () => {
    try {
        const newName = await showModal({
            type: 'prompt',
            title: '创建新世界书',
            text: '请输入新世界书的名称:',
            value: 'New-Lorebook',
        });

        if (typeof newName === 'string' && newName.trim()) {
            await createLorebook(newName.trim());
        }
    } catch (error) {
        console.log('Create lorebook operation cancelled.');
    }
};




// --- 正则表达式事件处理器 ---

const handleCreateRegex = async (scope: 'global' | 'character') => {
    try {
        const newRegexData = await showRegexEditorModal({
            regex: { script_name: '新正则', find_regex: '', replace_string: '' },
            isCreating: true,
        });
        await createRegex({ ...newRegexData, scope });
    } catch (error) {
        console.log('Create regex operation cancelled.');
    }
};

const handleEditRegex = async (regexId: string) => {
    const state = getState();
    const allRegexes = [...state.regexes.global, ...state.regexes.character];
    const regex = allRegexes.find(r => r.id === regexId);

    if (!regex) {
        console.error(`Regex with ID ${regexId} not found.`);
        return;
    }
    // 卡片内正则不可编辑
    if (regex.source === 'card') {
        await showModal({ type: 'alert', title: '操作无效', text: '无法编辑来自角色卡的正则表达式。' });
        return;
    }

    try {
        const updatedRegexData = await showRegexEditorModal({
            regex: { ...regex },
            isCreating: false,
        });
        await updateRegex(regexId, updatedRegexData);
    } catch (error) {
        console.log('Edit regex operation cancelled.');
    }
};

const handleDeleteRegex = async (regexId: string) => {
    const state = getState();
    const allRegexes = [...state.regexes.global, ...state.regexes.character];
    const regex = allRegexes.find(r => r.id === regexId);

    if (regex && regex.source === 'card') {
        await showModal({ type: 'alert', title: '操作无效', text: '无法删除来自角色卡的正则表达式。' });
        return;
    }
    
    try {
        const confirmation = await showModal({
            type: 'confirm',
            title: '确认删除',
            text: '你确定要永久删除这个正则表达式吗？',
        });
        if (confirmation) {
            await deleteRegex(regexId);
        }
    } catch (error) {
        console.log('Delete regex operation cancelled.');
    }
};