// src/world_info_optimizer/core.ts

import { TavernAPI } from './api';
import { updateState, setAllData, getState } from './store';
import { LorebookEntry, TavernRegex } from './types';

/**
 * 加载所有初始数据。
 * 这是应用启动时调用的主要数据获取函数。
 */
export const loadAllData = async () => {
    updateState(s => ({ ...s, isDataLoaded: false }));

    try {
        const context = TavernAPI.getContext();
        const { characters: allCharacters, characterId, chatId } = context;
        const hasActiveCharacter = characterId !== undefined && characterId !== null;
        const hasActiveChat = chatId !== undefined && chatId !== null;

        // --- 并行获取基础数据 ---
        const results = await Promise.allSettled([
            TavernAPI.getRegexes(),
            TavernAPI.getLorebookSettings(),
            TavernAPI.getLorebooks(),
            hasActiveCharacter ? TavernAPI.getCharData() : Promise.resolve(null),
            hasActiveCharacter ? TavernAPI.getCurrentCharLorebooks() : Promise.resolve(null),
            hasActiveChat ? TavernAPI.getChatLorebook() : Promise.resolve(null),
        ]);

        // --- 安全地从Promise结果中提取数据 ---
        const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];
        const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};
        const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];
        const charData = results[3].status === 'fulfilled' ? results[3].value : null;
        const charLinkedBooks = results[4].status === 'fulfilled' ? results[4].value : null;
        const chatLorebook = results[5].status === 'fulfilled' ? results[5].value : null;

        // --- 数据处理 ---

        // 1. 正则表达式处理
        const globalRegexes = (allUIRegexes || []).filter(r => r.scope === 'global');
        const characterRegexes = processCharacterRegexes(allUIRegexes, charData);

        // 2. 世界书文件和启用状态
        const enabledGlobalBooks = new Set(globalSettings?.selected_global_lorebooks || []);
        const allLorebooks = (allBookFileNames || []).map(name => ({
            name: name,
            enabled: enabledGlobalBooks.has(name)
        }));
        
        // 3. 当前角色关联的世界书
        const charBookSet = new Set<string>();
        if (charLinkedBooks?.primary) charBookSet.add(charLinkedBooks.primary);
        if (charLinkedBooks?.additional) charLinkedBooks.additional.forEach(b => charBookSet.add(b));
        const characterLorebooks = Array.from(charBookSet);
        
        // 4. 计算所有角色对世界书的使用情况 (lorebookUsage)
        const lorebookUsage = new Map<string, string[]>();
        const knownBookNames = new Set<string>(allBookFileNames || []);

        if (Array.isArray(allCharacters)) {
            for (const char of allCharacters) {
                if (!char?.name) continue;
                // 注意: getCharLorebooks 是同步还是异步取决于TavernHelper的实现
                // 为保险起见，我们假设它可能返回一个Promise
                const books = await TavernAPI.getCharLorebooks({ name: char.name });
                const charBooks = new Set<string>();
                if (books?.primary) charBooks.add(books.primary);
                if (books?.additional) books.additional.forEach(b => charBooks.add(b));
                
                charBooks.forEach(bookName => {
                    if (!lorebookUsage.has(bookName)) {
                        lorebookUsage.set(bookName, []);
                    }
                    lorebookUsage.get(bookName)?.push(char.name);
                    knownBookNames.add(bookName); // 确保所有被使用的书都被加载
                });
            }
        }
        
        // 5. 汇总所有需要加载条目的世界书
        characterLorebooks.forEach(b => knownBookNames.add(b));
        if (chatLorebook) {
            knownBookNames.add(chatLorebook);
        }

        // 6. 并行加载所有世界书的条目
        const lorebookEntries = new Map<string, LorebookEntry[]>();
        const entryPromises = Array.from(knownBookNames).map(async (name) => {
            const entries = await TavernAPI.getLorebookEntries(name);
            if (entries) {
                lorebookEntries.set(name, entries);
            }
        });
        await Promise.all(entryPromises);

        // 7. 更新全局状态
        setAllData({
            globalRegexes,
            characterRegexes,
            allLorebooks,
            characterLorebooks,
            chatLorebook: chatLorebook || null,
            lorebookEntries,
            lorebookUsage,
        });

    } catch (error) {
        console.error('[WIO Core] Failed to load all data:', error);
        updateState(s => ({ ...s, isDataLoaded: true })); // 即使失败也要结束加载状态
    }
};

/**
 * 处理和合并来自UI和角色卡中的角色特定正则表达式。
 * @param allUIRegexes 从Tavern API获取的所有UI正则
 * @param charData 当前角色的数据
 * @returns 合并和去重后的角色正则表达式数组
 */
function processCharacterRegexes(allUIRegexes: TavernRegex[] | null, charData: any): TavernRegex[] {
    const characterUIRegexes = allUIRegexes?.filter(r => r.scope === 'character') || [];
    let cardRegexes: TavernRegex[] = [];

    if (charData && TavernAPI.Character) {
        try {
            const character = new TavernAPI.Character(charData);
            cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({
                id: r.id || `card-${Date.now()}-${i}`,
                script_name: r.scriptName || '未命名卡内正则',
                find_regex: r.findRegex,
                replace_string: r.replaceString,
                enabled: !r.disabled,
                scope: 'character',
                source: 'card'
            }));
        } catch (e) {
            console.warn("[WIO Core] Couldn't parse character card regex scripts:", e);
        }
    }

    // 去重逻辑：UI中的正则优先
    const uiRegexIdentifiers = new Set(characterUIRegexes.map(r => `${r.script_name}::${r.find_regex}::${r.replace_string}`));
    const uniqueCardRegexes = cardRegexes.filter(r => {
        const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;
        return !uiRegexIdentifiers.has(identifier);
    });

    return [...characterUIRegexes, ...uniqueCardRegexes];
}

// --- Lorebook CRUD Operations ---

/**
 * 创建一个新的空世界书。
 * @param bookName 新世界书的名称
 */
export const createLorebook = async (bookName: string) => {
    const result = await TavernAPI.createLorebook(bookName);
    if (result !== null) {
        await loadAllData(); // 重新加载所有数据以确保状态同步
    }
};

/**
 * 重命名一个世界书。
 * @param oldName 旧名称
 * @param newName 新名称
 */
export const renameLorebook = async (oldName: string, newName: string) => {
    // 1. 获取旧书的所有条目
    const entries = await TavernAPI.getLorebookEntries(oldName);
    if (entries === null) {
        throw new Error(`Failed to get entries for lorebook: ${oldName}`);
    }

    // 2. 创建一个新书
    const createResult = await TavernAPI.createLorebook(newName);
    if (createResult === null) {
        throw new Error(`Failed to create new lorebook: ${newName}`);
    }

    // 3. 将条目复制到新书
    if (entries.length > 0) {
        const setResult = await TavernAPI.setLorebookEntries(newName, entries);
        if (setResult === null) {
            // 清理：如果条目设置失败，删除新建的书
            await TavernAPI.deleteLorebook(newName);
            throw new Error(`Failed to set entries for new lorebook: ${newName}`);
        }
    }

    // 4. 删除旧书
    const deleteResult = await TavernAPI.deleteLorebook(oldName);
    if (deleteResult === null) {
        // 这不是一个关键错误，但应该记录下来
        console.warn(`Failed to delete old lorebook "${oldName}" after renaming.`);
    }

    // 5. 刷新数据
    await loadAllData();
};


/**
 * 删除一个世界书。
 * @param bookName 要删除的世界书的名称
 */
export const deleteLorebook = async (bookName: string) => {
    const result = await TavernAPI.deleteLorebook(bookName);
    if (result !== null) {
        await loadAllData();
    }
};

// --- Lorebook Entry CRUD Operations ---

/**
 * 在指定的世界书中创建一个新条目。
 * @param bookName 世界书名称
 * @param entryData 要创建的条目的数据
 */
export const createLorebookEntry = async (bookName: string, entryData: LorebookEntry) => {
    // Tavern API需要一个数组
    const result = await TavernAPI.createLorebookEntries(bookName, [entryData]);
    if (result !== null && result.length > 0) {
        const newState = getState();
        const newEntry = result[0]; // API会返回创建后的条目，包含UID
        const currentEntries = newState.lorebookEntries.get(bookName) || [];
        newState.lorebookEntries.set(bookName, [...currentEntries, newEntry]);
        updateState(s => ({ ...s, lorebookEntries: new Map(newState.lorebookEntries) }));
    }
};

/**
 * 更新世界书中的一个现有条目。
 * @param bookName 世界书名称
 * @param uid 要更新的条目的唯一ID
 * @param updates 要应用于条目的部分更新
 */
export const updateLorebookEntry = async (bookName: string, uid: string, updates: Partial<LorebookEntry>) => {
    const state = getState();
    const entries = state.lorebookEntries.get(bookName);
    if (!entries) {
        throw new Error(`[WIO Core] Book not found in state: ${bookName}`);
    }

    let entryUpdated = false;
    const updatedEntries = entries.map(entry => {
        if (entry.uid === uid) {
            entryUpdated = true;
            return { ...entry, ...updates };
        }
        return entry;
    });

    if (entryUpdated) {
        const result = await TavernAPI.setLorebookEntries(bookName, updatedEntries);
        if (result !== null) {
            state.lorebookEntries.set(bookName, updatedEntries);
            updateState(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));
        }
    }
};

/**
 * 从世界书中删除一个条目。
 * @param bookName 世界书名称
 * @param uid 要删除的条目的唯一ID
 */
export const deleteLorebookEntry = async (bookName: string, uid: string) => {
    const result = await TavernAPI.deleteLorebookEntries(bookName, [uid]);
    if (result !== null) {
        const state = getState();
        const entries = state.lorebookEntries.get(bookName) || [];
        const filteredEntries = entries.filter(entry => entry.uid !== uid);
        state.lorebookEntries.set(bookName, filteredEntries);
        updateState(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));
    }
};

// --- Global Settings ---

/**
 * 设置一个全局世界书的启用状态。
 * @param bookName 世界书的名称
 * @param enabled true为启用, false为禁用
 */
export const setGlobalLorebookEnabled = async (bookName: string, enabled: boolean) => {
    const settings = await TavernAPI.getLorebookSettings() || {};
    if (!settings.selected_global_lorebooks) {
        settings.selected_global_lorebooks = [];
    }
    
    const enabledBooks = new Set(settings.selected_global_lorebooks);
    if (enabled) {
        enabledBooks.add(bookName);
    } else {
        enabledBooks.delete(bookName);
    }
    settings.selected_global_lorebooks = Array.from(enabledBooks);

    const result = await TavernAPI.setLorebookSettings(settings);
    if (result !== null) {
        // 更新本地状态以立即反映UI变化
        const state = getState();
        const book = state.allLorebooks.find(b => b.name === bookName);
        if (book) {
            book.enabled = enabled;
            updateState(s => ({ ...s, allLorebooks: [...state.allLorebooks] }));
        }
    }
};

// --- Regex CRUD Operations ---

/**
 * 提交正则表达式列表的更改。
 * @param updatedList 要提交的完整正则表达式列表 (仅限UI来源)
 */
const commitRegexChanges = async (updatedList: TavernRegex[]) => {
    const result = await TavernAPI.replaceRegexes(updatedList);
    if (result !== null) {
        // 成功后，重新加载所有数据以确保与后端完全同步
        // 这是最简单可靠的方式，避免复杂的本地状态管理
        await loadAllData();
    }
};

/**
 * 创建一个新的正则表达式。
 * @param newRegexData 要创建的正则表达式的数据
 */
export const createRegex = async (newRegexData: Partial<TavernRegex>) => {
    const state = getState();
    // 只处理UI来源的正则
    const currentUIRegexes = [
        ...state.regexes.global.filter(r => r.source !== 'card'),
        ...state.regexes.character.filter(r => r.source !== 'card')
    ];
    
    const finalNewRegex: TavernRegex = {
        id: `ui-${Date.now()}`,
        enabled: true,
        source: 'ui',
        ...newRegexData,
    } as TavernRegex;

    await commitRegexChanges([...currentUIRegexes, finalNewRegex]);
};

/**
 * 更新一个现有的正则表达式。
 * @param regexId 要更新的正则表达式的ID
 * @param updates 要应用的更新
 */
export const updateRegex = async (regexId: string, updates: Partial<TavernRegex>) => {
    const state = getState();
    const currentUIRegexes = [
        ...state.regexes.global.filter(r => r.source !== 'card'),
        ...state.regexes.character.filter(r => r.source !== 'card')
    ];

    const updatedList = currentUIRegexes.map(r =>
        r.id === regexId ? { ...r, ...updates } : r
    );

    await commitRegexChanges(updatedList);
};

/**
 * 删除一个正则表达式。
 * @param regexId 要删除的正则表达式的ID
 */
export const deleteRegex = async (regexId: string) => {
    const state = getState();
    const currentUIRegexes = [
        ...state.regexes.global.filter(r => r.source !== 'card'),
        ...state.regexes.character.filter(r => r.source !== 'card')
    ];

    const updatedList = currentUIRegexes.filter(r => r.id !== regexId);
    
    await commitRegexChanges(updatedList);
};